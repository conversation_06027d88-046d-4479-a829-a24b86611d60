#!/bin/bash
# <PERSON><PERSON>t to restart the script_manager container with debug logging enabled

echo "Stopping script_manager container..."
docker stop script_manager

echo "Removing script_manager container..."
docker rm script_manager

echo "Starting script_manager container with DEBUG logging..."
docker run -d \
  --name script_manager \
  -p 8000:8000 \
  --network kamal \
  -e PYTHONUNBUFFERED=1 \
  -e LOG_LEVEL=DEBUG \
  flamintdao/synctradepython

echo "Container restarted. Check logs with: docker logs -f script_manager"
