// flowbite@3.1.2 downloaded from https://ga.jspm.io/npm:flowbite@3.1.2/lib/esm/index.js

import{createPopper as t}from"@popperjs/core";import{DateRangePicker as e,Datepicker as i}from"flowbite-datepicker";var n=function(){function Events(t,e){e===void 0&&(e=[]);this._eventType=t;this._eventFunctions=e}Events.prototype.init=function(){var t=this;this._eventFunctions.forEach((function(e){typeof window!=="undefined"&&window.addEventListener(t._eventType,e)}))};return Events}();var o=function(){function Instances(){this._instances={Accordion:{},Carousel:{},Collapse:{},Dial:{},Dismiss:{},Drawer:{},Dropdown:{},Modal:{},Popover:{},Tabs:{},Tooltip:{},InputCounter:{},CopyClipboard:{},Datepicker:{}}}Instances.prototype.addInstance=function(t,e,i,n){n===void 0&&(n=false);if(!this._instances[t]){console.warn("Flowbite: Component ".concat(t," does not exist."));return false}if(!this._instances[t][i]||n){n&&this._instances[t][i]&&this._instances[t][i].destroyAndRemoveInstance();this._instances[t][i||this._generateRandomId()]=e}else console.warn("Flowbite: Instance with ID ".concat(i," already exists."))};Instances.prototype.getAllInstances=function(){return this._instances};Instances.prototype.getInstances=function(t){if(!this._instances[t]){console.warn("Flowbite: Component ".concat(t," does not exist."));return false}return this._instances[t]};Instances.prototype.getInstance=function(t,e){if(this._componentAndInstanceCheck(t,e)){if(this._instances[t][e])return this._instances[t][e];console.warn("Flowbite: Instance with ID ".concat(e," does not exist."))}};Instances.prototype.destroyAndRemoveInstance=function(t,e){if(this._componentAndInstanceCheck(t,e)){this.destroyInstanceObject(t,e);this.removeInstance(t,e)}};Instances.prototype.removeInstance=function(t,e){this._componentAndInstanceCheck(t,e)&&delete this._instances[t][e]};Instances.prototype.destroyInstanceObject=function(t,e){this._componentAndInstanceCheck(t,e)&&this._instances[t][e].destroy()};Instances.prototype.instanceExists=function(t,e){return!!this._instances[t]&&!!this._instances[t][e]};Instances.prototype._generateRandomId=function(){return Math.random().toString(36).substr(2,9)};Instances.prototype._componentAndInstanceCheck=function(t,e){if(!this._instances[t]){console.warn("Flowbite: Component ".concat(t," does not exist."));return false}if(!this._instances[t][e]){console.warn("Flowbite: Instance with ID ".concat(e," does not exist."));return false}return true};return Instances}();var s=new o;typeof window!=="undefined"&&(window.FlowbiteInstances=s);var r=(void 0,function(){r=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return r.apply(this,arguments)});var a={alwaysOpen:false,activeClasses:"bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white",inactiveClasses:"text-gray-500 dark:text-gray-400",onOpen:function(){},onClose:function(){},onToggle:function(){}};var l={id:null,override:true};var d=function(){function Accordion(t,e,i,n){t===void 0&&(t=null);e===void 0&&(e=[]);i===void 0&&(i=a);n===void 0&&(n=l);this._instanceId=n.id?n.id:t.id;this._accordionEl=t;this._items=e;this._options=r(r({},a),i);this._initialized=false;this.init();s.addInstance("Accordion",this,this._instanceId,n.override)}Accordion.prototype.init=function(){var t=this;if(this._items.length&&!this._initialized){this._items.forEach((function(e){e.active&&t.open(e.id);var clickHandler=function(){t.toggle(e.id)};e.triggerEl.addEventListener("click",clickHandler);e.clickHandler=clickHandler}));this._initialized=true}};Accordion.prototype.destroy=function(){if(this._items.length&&this._initialized){this._items.forEach((function(t){t.triggerEl.removeEventListener("click",t.clickHandler);delete t.clickHandler}));this._initialized=false}};Accordion.prototype.removeInstance=function(){s.removeInstance("Accordion",this._instanceId)};Accordion.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Accordion.prototype.getItem=function(t){return this._items.filter((function(e){return e.id===t}))[0]};Accordion.prototype.open=function(t){var e,i;var n=this;var o=this.getItem(t);this._options.alwaysOpen||this._items.map((function(t){var e,i;if(t!==o){(e=t.triggerEl.classList).remove.apply(e,n._options.activeClasses.split(" "));(i=t.triggerEl.classList).add.apply(i,n._options.inactiveClasses.split(" "));t.targetEl.classList.add("hidden");t.triggerEl.setAttribute("aria-expanded","false");t.active=false;t.iconEl&&t.iconEl.classList.add("rotate-180")}}));(e=o.triggerEl.classList).add.apply(e,this._options.activeClasses.split(" "));(i=o.triggerEl.classList).remove.apply(i,this._options.inactiveClasses.split(" "));o.triggerEl.setAttribute("aria-expanded","true");o.targetEl.classList.remove("hidden");o.active=true;o.iconEl&&o.iconEl.classList.remove("rotate-180");this._options.onOpen(this,o)};Accordion.prototype.toggle=function(t){var e=this.getItem(t);e.active?this.close(t):this.open(t);this._options.onToggle(this,e)};Accordion.prototype.close=function(t){var e,i;var n=this.getItem(t);(e=n.triggerEl.classList).remove.apply(e,this._options.activeClasses.split(" "));(i=n.triggerEl.classList).add.apply(i,this._options.inactiveClasses.split(" "));n.targetEl.classList.add("hidden");n.triggerEl.setAttribute("aria-expanded","false");n.active=false;n.iconEl&&n.iconEl.classList.add("rotate-180");this._options.onClose(this,n)};Accordion.prototype.updateOnOpen=function(t){this._options.onOpen=t};Accordion.prototype.updateOnClose=function(t){this._options.onClose=t};Accordion.prototype.updateOnToggle=function(t){this._options.onToggle=t};return Accordion}();function initAccordions(){document.querySelectorAll("[data-accordion]").forEach((function(t){var e=t.getAttribute("data-accordion");var i=t.getAttribute("data-active-classes");var n=t.getAttribute("data-inactive-classes");var o=[];t.querySelectorAll("[data-accordion-target]").forEach((function(e){if(e.closest("[data-accordion]")===t){var i={id:e.getAttribute("data-accordion-target"),triggerEl:e,targetEl:document.querySelector(e.getAttribute("data-accordion-target")),iconEl:e.querySelector("[data-accordion-icon]"),active:e.getAttribute("aria-expanded")==="true"};o.push(i)}}));new d(t,o,{alwaysOpen:e==="open",activeClasses:i||a.activeClasses,inactiveClasses:n||a.inactiveClasses})}))}if(typeof window!=="undefined"){window.Accordion=d;window.initAccordions=initAccordions}var c=(void 0,function(){c=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return c.apply(this,arguments)});var p={onCollapse:function(){},onExpand:function(){},onToggle:function(){}};var h={id:null,override:true};var u=function(){function Collapse(t,e,i,n){t===void 0&&(t=null);e===void 0&&(e=null);i===void 0&&(i=p);n===void 0&&(n=h);this._instanceId=n.id?n.id:t.id;this._targetEl=t;this._triggerEl=e;this._options=c(c({},p),i);this._visible=false;this._initialized=false;this.init();s.addInstance("Collapse",this,this._instanceId,n.override)}Collapse.prototype.init=function(){var t=this;if(this._triggerEl&&this._targetEl&&!this._initialized){this._triggerEl.hasAttribute("aria-expanded")?this._visible=this._triggerEl.getAttribute("aria-expanded")==="true":this._visible=!this._targetEl.classList.contains("hidden");this._clickHandler=function(){t.toggle()};this._triggerEl.addEventListener("click",this._clickHandler);this._initialized=true}};Collapse.prototype.destroy=function(){if(this._triggerEl&&this._initialized){this._triggerEl.removeEventListener("click",this._clickHandler);this._initialized=false}};Collapse.prototype.removeInstance=function(){s.removeInstance("Collapse",this._instanceId)};Collapse.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Collapse.prototype.collapse=function(){this._targetEl.classList.add("hidden");this._triggerEl&&this._triggerEl.setAttribute("aria-expanded","false");this._visible=false;this._options.onCollapse(this)};Collapse.prototype.expand=function(){this._targetEl.classList.remove("hidden");this._triggerEl&&this._triggerEl.setAttribute("aria-expanded","true");this._visible=true;this._options.onExpand(this)};Collapse.prototype.toggle=function(){this._visible?this.collapse():this.expand();this._options.onToggle(this)};Collapse.prototype.updateOnCollapse=function(t){this._options.onCollapse=t};Collapse.prototype.updateOnExpand=function(t){this._options.onExpand=t};Collapse.prototype.updateOnToggle=function(t){this._options.onToggle=t};return Collapse}();function initCollapses(){document.querySelectorAll("[data-collapse-toggle]").forEach((function(t){var e=t.getAttribute("data-collapse-toggle");var i=document.getElementById(e);i?s.instanceExists("Collapse",i.getAttribute("id"))?new u(i,t,{},{id:i.getAttribute("id")+"_"+s._generateRandomId()}):new u(i,t):console.error('The target element with id "'.concat(e,'" does not exist. Please check the data-collapse-toggle attribute.'))}))}if(typeof window!=="undefined"){window.Collapse=u;window.initCollapses=initCollapses}var v=(void 0,function(){v=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return v.apply(this,arguments)});var f={defaultPosition:0,indicators:{items:[],activeClasses:"bg-white dark:bg-gray-800",inactiveClasses:"bg-white/50 dark:bg-gray-800/50 hover:bg-white dark:hover:bg-gray-800"},interval:3e3,onNext:function(){},onPrev:function(){},onChange:function(){}};var g={id:null,override:true};var _=function(){function Carousel(t,e,i,n){t===void 0&&(t=null);e===void 0&&(e=[]);i===void 0&&(i=f);n===void 0&&(n=g);this._instanceId=n.id?n.id:t.id;this._carouselEl=t;this._items=e;this._options=v(v(v({},f),i),{indicators:v(v({},f.indicators),i.indicators)});this._activeItem=this.getItem(this._options.defaultPosition);this._indicators=this._options.indicators.items;this._intervalDuration=this._options.interval;this._intervalInstance=null;this._initialized=false;this.init();s.addInstance("Carousel",this,this._instanceId,n.override)}Carousel.prototype.init=function(){var t=this;if(this._items.length&&!this._initialized){this._items.map((function(t){t.el.classList.add("absolute","inset-0","transition-transform","transform")}));this.getActiveItem()?this.slideTo(this.getActiveItem().position):this.slideTo(0);this._indicators.map((function(e,i){e.el.addEventListener("click",(function(){t.slideTo(i)}))}));this._initialized=true}};Carousel.prototype.destroy=function(){this._initialized&&(this._initialized=false)};Carousel.prototype.removeInstance=function(){s.removeInstance("Carousel",this._instanceId)};Carousel.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Carousel.prototype.getItem=function(t){return this._items[t]};
/**
     * Slide to the element based on id
     * @param {*} position
     */Carousel.prototype.slideTo=function(t){var e=this._items[t];var i={left:e.position===0?this._items[this._items.length-1]:this._items[e.position-1],middle:e,right:e.position===this._items.length-1?this._items[0]:this._items[e.position+1]};this._rotate(i);this._setActiveItem(e);if(this._intervalInstance){this.pause();this.cycle()}this._options.onChange(this)};Carousel.prototype.next=function(){var t=this.getActiveItem();var e=null;e=t.position===this._items.length-1?this._items[0]:this._items[t.position+1];this.slideTo(e.position);this._options.onNext(this)};Carousel.prototype.prev=function(){var t=this.getActiveItem();var e=null;e=t.position===0?this._items[this._items.length-1]:this._items[t.position-1];this.slideTo(e.position);this._options.onPrev(this)};
/**
     * This method applies the transform classes based on the left, middle, and right rotation carousel items
     * @param {*} rotationItems
     */Carousel.prototype._rotate=function(t){this._items.map((function(t){t.el.classList.add("hidden")}));if(this._items.length!==1){t.left.el.classList.remove("-translate-x-full","translate-x-full","translate-x-0","hidden","z-20");t.left.el.classList.add("-translate-x-full","z-10");t.middle.el.classList.remove("-translate-x-full","translate-x-full","translate-x-0","hidden","z-10");t.middle.el.classList.add("translate-x-0","z-30");t.right.el.classList.remove("-translate-x-full","translate-x-full","translate-x-0","hidden","z-30");t.right.el.classList.add("translate-x-full","z-20")}else{t.middle.el.classList.remove("-translate-x-full","translate-x-full","translate-x-0","hidden","z-10");t.middle.el.classList.add("translate-x-0","z-20")}};Carousel.prototype.cycle=function(){var t=this;typeof window!=="undefined"&&(this._intervalInstance=window.setInterval((function(){t.next()}),this._intervalDuration))};Carousel.prototype.pause=function(){clearInterval(this._intervalInstance)};Carousel.prototype.getActiveItem=function(){return this._activeItem};
/**
     * Set the currently active item and data attribute
     * @param {*} position
     */Carousel.prototype._setActiveItem=function(t){var e,i;var n=this;this._activeItem=t;var o=t.position;if(this._indicators.length){this._indicators.map((function(t){var e,i;t.el.setAttribute("aria-current","false");(e=t.el.classList).remove.apply(e,n._options.indicators.activeClasses.split(" "));(i=t.el.classList).add.apply(i,n._options.indicators.inactiveClasses.split(" "))}));(e=this._indicators[o].el.classList).add.apply(e,this._options.indicators.activeClasses.split(" "));(i=this._indicators[o].el.classList).remove.apply(i,this._options.indicators.inactiveClasses.split(" "));this._indicators[o].el.setAttribute("aria-current","true")}};Carousel.prototype.updateOnNext=function(t){this._options.onNext=t};Carousel.prototype.updateOnPrev=function(t){this._options.onPrev=t};Carousel.prototype.updateOnChange=function(t){this._options.onChange=t};return Carousel}();function initCarousels(){document.querySelectorAll("[data-carousel]").forEach((function(t){var e=t.getAttribute("data-carousel-interval");var i=t.getAttribute("data-carousel")==="slide";var n=[];var o=0;t.querySelectorAll("[data-carousel-item]").length&&Array.from(t.querySelectorAll("[data-carousel-item]")).map((function(t,e){n.push({position:e,el:t});t.getAttribute("data-carousel-item")==="active"&&(o=e)}));var s=[];t.querySelectorAll("[data-carousel-slide-to]").length&&Array.from(t.querySelectorAll("[data-carousel-slide-to]")).map((function(t){s.push({position:parseInt(t.getAttribute("data-carousel-slide-to")),el:t})}));var r=new _(t,n,{defaultPosition:o,indicators:{items:s},interval:e||f.interval});i&&r.cycle();var a=t.querySelector("[data-carousel-next]");var l=t.querySelector("[data-carousel-prev]");a&&a.addEventListener("click",(function(){r.next()}));l&&l.addEventListener("click",(function(){r.prev()}))}))}if(typeof window!=="undefined"){window.Carousel=_;window.initCarousels=initCarousels}var y=(void 0,function(){y=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return y.apply(this,arguments)});var m={transition:"transition-opacity",duration:300,timing:"ease-out",onHide:function(){}};var E={id:null,override:true};var w=function(){function Dismiss(t,e,i,n){t===void 0&&(t=null);e===void 0&&(e=null);i===void 0&&(i=m);n===void 0&&(n=E);this._instanceId=n.id?n.id:t.id;this._targetEl=t;this._triggerEl=e;this._options=y(y({},m),i);this._initialized=false;this.init();s.addInstance("Dismiss",this,this._instanceId,n.override)}Dismiss.prototype.init=function(){var t=this;if(this._triggerEl&&this._targetEl&&!this._initialized){this._clickHandler=function(){t.hide()};this._triggerEl.addEventListener("click",this._clickHandler);this._initialized=true}};Dismiss.prototype.destroy=function(){if(this._triggerEl&&this._initialized){this._triggerEl.removeEventListener("click",this._clickHandler);this._initialized=false}};Dismiss.prototype.removeInstance=function(){s.removeInstance("Dismiss",this._instanceId)};Dismiss.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Dismiss.prototype.hide=function(){var t=this;this._targetEl.classList.add(this._options.transition,"duration-".concat(this._options.duration),this._options.timing,"opacity-0");setTimeout((function(){t._targetEl.classList.add("hidden")}),this._options.duration);this._options.onHide(this,this._targetEl)};Dismiss.prototype.updateOnHide=function(t){this._options.onHide=t};return Dismiss}();function initDismisses(){document.querySelectorAll("[data-dismiss-target]").forEach((function(t){var e=t.getAttribute("data-dismiss-target");var i=document.querySelector(e);i?new w(i,t):console.error('The dismiss element with id "'.concat(e,'" does not exist. Please check the data-dismiss-target attribute.'))}))}if(typeof window!=="undefined"){window.Dismiss=w;window.initDismisses=initDismisses}var b=(void 0,function(){b=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return b.apply(this,arguments)});var I=(void 0,function(t,e,i){if(i||arguments.length===2)for(var n,o=0,s=e.length;o<s;o++)if(n||!(o in e)){n||(n=Array.prototype.slice.call(e,0,o));n[o]=e[o]}return t.concat(n||Array.prototype.slice.call(e))});var k={placement:"bottom",triggerType:"click",offsetSkidding:0,offsetDistance:10,delay:300,ignoreClickOutsideClass:false,onShow:function(){},onHide:function(){},onToggle:function(){}};var C={id:null,override:true};var L=function(){function Dropdown(t,e,i,n){t===void 0&&(t=null);e===void 0&&(e=null);i===void 0&&(i=k);n===void 0&&(n=C);this._instanceId=n.id?n.id:t.id;this._targetEl=t;this._triggerEl=e;this._options=b(b({},k),i);this._popperInstance=null;this._visible=false;this._initialized=false;this.init();s.addInstance("Dropdown",this,this._instanceId,n.override)}Dropdown.prototype.init=function(){if(this._triggerEl&&this._targetEl&&!this._initialized){this._popperInstance=this._createPopperInstance();this._setupEventListeners();this._initialized=true}};Dropdown.prototype.destroy=function(){var t=this;var e=this._getTriggerEvents();this._options.triggerType==="click"&&e.showEvents.forEach((function(e){t._triggerEl.removeEventListener(e,t._clickHandler)}));if(this._options.triggerType==="hover"){e.showEvents.forEach((function(e){t._triggerEl.removeEventListener(e,t._hoverShowTriggerElHandler);t._targetEl.removeEventListener(e,t._hoverShowTargetElHandler)}));e.hideEvents.forEach((function(e){t._triggerEl.removeEventListener(e,t._hoverHideHandler);t._targetEl.removeEventListener(e,t._hoverHideHandler)}))}this._popperInstance.destroy();this._initialized=false};Dropdown.prototype.removeInstance=function(){s.removeInstance("Dropdown",this._instanceId)};Dropdown.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Dropdown.prototype._setupEventListeners=function(){var t=this;var e=this._getTriggerEvents();this._clickHandler=function(){t.toggle()};this._options.triggerType==="click"&&e.showEvents.forEach((function(e){t._triggerEl.addEventListener(e,t._clickHandler)}));this._hoverShowTriggerElHandler=function(e){e.type==="click"?t.toggle():setTimeout((function(){t.show()}),t._options.delay)};this._hoverShowTargetElHandler=function(){t.show()};this._hoverHideHandler=function(){setTimeout((function(){t._targetEl.matches(":hover")||t.hide()}),t._options.delay)};if(this._options.triggerType==="hover"){e.showEvents.forEach((function(e){t._triggerEl.addEventListener(e,t._hoverShowTriggerElHandler);t._targetEl.addEventListener(e,t._hoverShowTargetElHandler)}));e.hideEvents.forEach((function(e){t._triggerEl.addEventListener(e,t._hoverHideHandler);t._targetEl.addEventListener(e,t._hoverHideHandler)}))}};Dropdown.prototype._createPopperInstance=function(){return t(this._triggerEl,this._targetEl,{placement:this._options.placement,modifiers:[{name:"offset",options:{offset:[this._options.offsetSkidding,this._options.offsetDistance]}}]})};Dropdown.prototype._setupClickOutsideListener=function(){var t=this;this._clickOutsideEventListener=function(e){t._handleClickOutside(e,t._targetEl)};document.body.addEventListener("click",this._clickOutsideEventListener,true)};Dropdown.prototype._removeClickOutsideListener=function(){document.body.removeEventListener("click",this._clickOutsideEventListener,true)};Dropdown.prototype._handleClickOutside=function(t,e){var i=t.target;var n=this._options.ignoreClickOutsideClass;var o=false;if(n){var s=document.querySelectorAll(".".concat(n));s.forEach((function(t){t.contains(i)&&(o=true)}))}i===e||e.contains(i)||this._triggerEl.contains(i)||o||!this.isVisible()||this.hide()};Dropdown.prototype._getTriggerEvents=function(){switch(this._options.triggerType){case"hover":return{showEvents:["mouseenter","click"],hideEvents:["mouseleave"]};case"click":return{showEvents:["click"],hideEvents:[]};case"none":return{showEvents:[],hideEvents:[]};default:return{showEvents:["click"],hideEvents:[]}}};Dropdown.prototype.toggle=function(){this.isVisible()?this.hide():this.show();this._options.onToggle(this)};Dropdown.prototype.isVisible=function(){return this._visible};Dropdown.prototype.show=function(){this._targetEl.classList.remove("hidden");this._targetEl.classList.add("block");this._targetEl.removeAttribute("aria-hidden");this._popperInstance.setOptions((function(t){return b(b({},t),{modifiers:I(I([],t.modifiers,true),[{name:"eventListeners",enabled:true}],false)})}));this._setupClickOutsideListener();this._popperInstance.update();this._visible=true;this._options.onShow(this)};Dropdown.prototype.hide=function(){this._targetEl.classList.remove("block");this._targetEl.classList.add("hidden");this._targetEl.setAttribute("aria-hidden","true");this._popperInstance.setOptions((function(t){return b(b({},t),{modifiers:I(I([],t.modifiers,true),[{name:"eventListeners",enabled:false}],false)})}));this._visible=false;this._removeClickOutsideListener();this._options.onHide(this)};Dropdown.prototype.updateOnShow=function(t){this._options.onShow=t};Dropdown.prototype.updateOnHide=function(t){this._options.onHide=t};Dropdown.prototype.updateOnToggle=function(t){this._options.onToggle=t};return Dropdown}();function initDropdowns(){document.querySelectorAll("[data-dropdown-toggle]").forEach((function(t){var e=t.getAttribute("data-dropdown-toggle");var i=document.getElementById(e);if(i){var n=t.getAttribute("data-dropdown-placement");var o=t.getAttribute("data-dropdown-offset-skidding");var s=t.getAttribute("data-dropdown-offset-distance");var r=t.getAttribute("data-dropdown-trigger");var a=t.getAttribute("data-dropdown-delay");var l=t.getAttribute("data-dropdown-ignore-click-outside-class");new L(i,t,{placement:n||k.placement,triggerType:r||k.triggerType,offsetSkidding:o?parseInt(o):k.offsetSkidding,offsetDistance:s?parseInt(s):k.offsetDistance,delay:a?parseInt(a):k.delay,ignoreClickOutsideClass:l||k.ignoreClickOutsideClass})}else console.error('The dropdown element with id "'.concat(e,'" does not exist. Please check the data-dropdown-toggle attribute.'))}))}if(typeof window!=="undefined"){window.Dropdown=L;window.initDropdowns=initDropdowns}var A=(void 0,function(){A=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return A.apply(this,arguments)});var D={placement:"center",backdropClasses:"bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-40",backdrop:"dynamic",closable:true,onHide:function(){},onShow:function(){},onToggle:function(){}};var T={id:null,override:true};var O=function(){function Modal(t,e,i){t===void 0&&(t=null);e===void 0&&(e=D);i===void 0&&(i=T);this._eventListenerInstances=[];this._instanceId=i.id?i.id:t.id;this._targetEl=t;this._options=A(A({},D),e);this._isHidden=true;this._backdropEl=null;this._initialized=false;this.init();s.addInstance("Modal",this,this._instanceId,i.override)}Modal.prototype.init=function(){var t=this;if(this._targetEl&&!this._initialized){this._getPlacementClasses().map((function(e){t._targetEl.classList.add(e)}));this._initialized=true}};Modal.prototype.destroy=function(){if(this._initialized){this.removeAllEventListenerInstances();this._destroyBackdropEl();this._initialized=false}};Modal.prototype.removeInstance=function(){s.removeInstance("Modal",this._instanceId)};Modal.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Modal.prototype._createBackdrop=function(){var t;if(this._isHidden){var e=document.createElement("div");(t=e.classList).add.apply(t,this._options.backdropClasses.split(" "));document.querySelector("body").append(e);this._backdropEl=e}};Modal.prototype._destroyBackdropEl=function(){if(!this._isHidden&&this._backdropEl){this._backdropEl.remove();this._backdropEl=null}};Modal.prototype._setupModalCloseEventListeners=function(){var t=this;if(this._options.backdrop==="dynamic"){this._clickOutsideEventListener=function(e){t._handleOutsideClick(e.target)};this._targetEl.addEventListener("click",this._clickOutsideEventListener,true)}this._keydownEventListener=function(e){e.key==="Escape"&&t.hide()};document.body.addEventListener("keydown",this._keydownEventListener,true)};Modal.prototype._removeModalCloseEventListeners=function(){this._options.backdrop==="dynamic"&&this._targetEl.removeEventListener("click",this._clickOutsideEventListener,true);document.body.removeEventListener("keydown",this._keydownEventListener,true)};Modal.prototype._handleOutsideClick=function(t){(t===this._targetEl||t===this._backdropEl&&this.isVisible())&&this.hide()};Modal.prototype._getPlacementClasses=function(){switch(this._options.placement){case"top-left":return["justify-start","items-start"];case"top-center":return["justify-center","items-start"];case"top-right":return["justify-end","items-start"];case"center-left":return["justify-start","items-center"];case"center":return["justify-center","items-center"];case"center-right":return["justify-end","items-center"];case"bottom-left":return["justify-start","items-end"];case"bottom-center":return["justify-center","items-end"];case"bottom-right":return["justify-end","items-end"];default:return["justify-center","items-center"]}};Modal.prototype.toggle=function(){this._isHidden?this.show():this.hide();this._options.onToggle(this)};Modal.prototype.show=function(){if(this.isHidden){this._targetEl.classList.add("flex");this._targetEl.classList.remove("hidden");this._targetEl.setAttribute("aria-modal","true");this._targetEl.setAttribute("role","dialog");this._targetEl.removeAttribute("aria-hidden");this._createBackdrop();this._isHidden=false;this._options.closable&&this._setupModalCloseEventListeners();document.body.classList.add("overflow-hidden");this._options.onShow(this)}};Modal.prototype.hide=function(){if(this.isVisible){this._targetEl.classList.add("hidden");this._targetEl.classList.remove("flex");this._targetEl.setAttribute("aria-hidden","true");this._targetEl.removeAttribute("aria-modal");this._targetEl.removeAttribute("role");this._destroyBackdropEl();this._isHidden=true;document.body.classList.remove("overflow-hidden");this._options.closable&&this._removeModalCloseEventListeners();this._options.onHide(this)}};Modal.prototype.isVisible=function(){return!this._isHidden};Modal.prototype.isHidden=function(){return this._isHidden};Modal.prototype.addEventListenerInstance=function(t,e,i){this._eventListenerInstances.push({element:t,type:e,handler:i})};Modal.prototype.removeAllEventListenerInstances=function(){this._eventListenerInstances.map((function(t){t.element.removeEventListener(t.type,t.handler)}));this._eventListenerInstances=[]};Modal.prototype.getAllEventListenerInstances=function(){return this._eventListenerInstances};Modal.prototype.updateOnShow=function(t){this._options.onShow=t};Modal.prototype.updateOnHide=function(t){this._options.onHide=t};Modal.prototype.updateOnToggle=function(t){this._options.onToggle=t};return Modal}();function initModals(){document.querySelectorAll("[data-modal-target]").forEach((function(t){var e=t.getAttribute("data-modal-target");var i=document.getElementById(e);if(i){var n=i.getAttribute("data-modal-placement");var o=i.getAttribute("data-modal-backdrop");new O(i,{placement:n||D.placement,backdrop:o||D.backdrop})}else console.error("Modal with id ".concat(e," does not exist. Are you sure that the data-modal-target attribute points to the correct modal id?."))}));document.querySelectorAll("[data-modal-toggle]").forEach((function(t){var e=t.getAttribute("data-modal-toggle");var i=document.getElementById(e);if(i){var n=s.getInstance("Modal",e);if(n){var toggleModal=function(){n.toggle()};t.addEventListener("click",toggleModal);n.addEventListenerInstance(t,"click",toggleModal)}else console.error("Modal with id ".concat(e," has not been initialized. Please initialize it using the data-modal-target attribute."))}else console.error("Modal with id ".concat(e," does not exist. Are you sure that the data-modal-toggle attribute points to the correct modal id?"))}));document.querySelectorAll("[data-modal-show]").forEach((function(t){var e=t.getAttribute("data-modal-show");var i=document.getElementById(e);if(i){var n=s.getInstance("Modal",e);if(n){var showModal=function(){n.show()};t.addEventListener("click",showModal);n.addEventListenerInstance(t,"click",showModal)}else console.error("Modal with id ".concat(e," has not been initialized. Please initialize it using the data-modal-target attribute."))}else console.error("Modal with id ".concat(e," does not exist. Are you sure that the data-modal-show attribute points to the correct modal id?"))}));document.querySelectorAll("[data-modal-hide]").forEach((function(t){var e=t.getAttribute("data-modal-hide");var i=document.getElementById(e);if(i){var n=s.getInstance("Modal",e);if(n){var hideModal=function(){n.hide()};t.addEventListener("click",hideModal);n.addEventListenerInstance(t,"click",hideModal)}else console.error("Modal with id ".concat(e," has not been initialized. Please initialize it using the data-modal-target attribute."))}else console.error("Modal with id ".concat(e," does not exist. Are you sure that the data-modal-hide attribute points to the correct modal id?"))}))}if(typeof window!=="undefined"){window.Modal=O;window.initModals=initModals}var H=(void 0,function(){H=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return H.apply(this,arguments)});var x={placement:"left",bodyScrolling:false,backdrop:true,edge:false,edgeOffset:"bottom-[60px]",backdropClasses:"bg-gray-900/50 dark:bg-gray-900/80 fixed inset-0 z-30",onShow:function(){},onHide:function(){},onToggle:function(){}};var S={id:null,override:true};var P=function(){function Drawer(t,e,i){t===void 0&&(t=null);e===void 0&&(e=x);i===void 0&&(i=S);this._eventListenerInstances=[];this._instanceId=i.id?i.id:t.id;this._targetEl=t;this._options=H(H({},x),e);this._visible=false;this._initialized=false;this.init();s.addInstance("Drawer",this,this._instanceId,i.override)}Drawer.prototype.init=function(){var t=this;if(this._targetEl&&!this._initialized){this._targetEl.setAttribute("aria-hidden","true");this._targetEl.classList.add("transition-transform");this._getPlacementClasses(this._options.placement).base.map((function(e){t._targetEl.classList.add(e)}));this._handleEscapeKey=function(e){e.key==="Escape"&&t.isVisible()&&t.hide()};document.addEventListener("keydown",this._handleEscapeKey);this._initialized=true}};Drawer.prototype.destroy=function(){if(this._initialized){this.removeAllEventListenerInstances();this._destroyBackdropEl();document.removeEventListener("keydown",this._handleEscapeKey);this._initialized=false}};Drawer.prototype.removeInstance=function(){s.removeInstance("Drawer",this._instanceId)};Drawer.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Drawer.prototype.hide=function(){var t=this;if(this._options.edge){this._getPlacementClasses(this._options.placement+"-edge").active.map((function(e){t._targetEl.classList.remove(e)}));this._getPlacementClasses(this._options.placement+"-edge").inactive.map((function(e){t._targetEl.classList.add(e)}))}else{this._getPlacementClasses(this._options.placement).active.map((function(e){t._targetEl.classList.remove(e)}));this._getPlacementClasses(this._options.placement).inactive.map((function(e){t._targetEl.classList.add(e)}))}this._targetEl.setAttribute("aria-hidden","true");this._targetEl.removeAttribute("aria-modal");this._targetEl.removeAttribute("role");this._options.bodyScrolling||document.body.classList.remove("overflow-hidden");this._options.backdrop&&this._destroyBackdropEl();this._visible=false;this._options.onHide(this)};Drawer.prototype.show=function(){var t=this;if(this._options.edge){this._getPlacementClasses(this._options.placement+"-edge").active.map((function(e){t._targetEl.classList.add(e)}));this._getPlacementClasses(this._options.placement+"-edge").inactive.map((function(e){t._targetEl.classList.remove(e)}))}else{this._getPlacementClasses(this._options.placement).active.map((function(e){t._targetEl.classList.add(e)}));this._getPlacementClasses(this._options.placement).inactive.map((function(e){t._targetEl.classList.remove(e)}))}this._targetEl.setAttribute("aria-modal","true");this._targetEl.setAttribute("role","dialog");this._targetEl.removeAttribute("aria-hidden");this._options.bodyScrolling||document.body.classList.add("overflow-hidden");this._options.backdrop&&this._createBackdrop();this._visible=true;this._options.onShow(this)};Drawer.prototype.toggle=function(){this.isVisible()?this.hide():this.show()};Drawer.prototype._createBackdrop=function(){var t;var e=this;if(!this._visible){var i=document.createElement("div");i.setAttribute("drawer-backdrop","");(t=i.classList).add.apply(t,this._options.backdropClasses.split(" "));document.querySelector("body").append(i);i.addEventListener("click",(function(){e.hide()}))}};Drawer.prototype._destroyBackdropEl=function(){this._visible&&document.querySelector("[drawer-backdrop]")!==null&&document.querySelector("[drawer-backdrop]").remove()};Drawer.prototype._getPlacementClasses=function(t){switch(t){case"top":return{base:["top-0","left-0","right-0"],active:["transform-none"],inactive:["-translate-y-full"]};case"right":return{base:["right-0","top-0"],active:["transform-none"],inactive:["translate-x-full"]};case"bottom":return{base:["bottom-0","left-0","right-0"],active:["transform-none"],inactive:["translate-y-full"]};case"left":return{base:["left-0","top-0"],active:["transform-none"],inactive:["-translate-x-full"]};case"bottom-edge":return{base:["left-0","top-0"],active:["transform-none"],inactive:["translate-y-full",this._options.edgeOffset]};default:return{base:["left-0","top-0"],active:["transform-none"],inactive:["-translate-x-full"]}}};Drawer.prototype.isHidden=function(){return!this._visible};Drawer.prototype.isVisible=function(){return this._visible};Drawer.prototype.addEventListenerInstance=function(t,e,i){this._eventListenerInstances.push({element:t,type:e,handler:i})};Drawer.prototype.removeAllEventListenerInstances=function(){this._eventListenerInstances.map((function(t){t.element.removeEventListener(t.type,t.handler)}));this._eventListenerInstances=[]};Drawer.prototype.getAllEventListenerInstances=function(){return this._eventListenerInstances};Drawer.prototype.updateOnShow=function(t){this._options.onShow=t};Drawer.prototype.updateOnHide=function(t){this._options.onHide=t};Drawer.prototype.updateOnToggle=function(t){this._options.onToggle=t};return Drawer}();function initDrawers(){document.querySelectorAll("[data-drawer-target]").forEach((function(t){var e=t.getAttribute("data-drawer-target");var i=document.getElementById(e);if(i){var n=t.getAttribute("data-drawer-placement");var o=t.getAttribute("data-drawer-body-scrolling");var s=t.getAttribute("data-drawer-backdrop");var r=t.getAttribute("data-drawer-edge");var a=t.getAttribute("data-drawer-edge-offset");new P(i,{placement:n||x.placement,bodyScrolling:o?o==="true":x.bodyScrolling,backdrop:s?s==="true":x.backdrop,edge:r?r==="true":x.edge,edgeOffset:a||x.edgeOffset})}else console.error("Drawer with id ".concat(e," not found. Are you sure that the data-drawer-target attribute points to the correct drawer id?"))}));document.querySelectorAll("[data-drawer-toggle]").forEach((function(t){var e=t.getAttribute("data-drawer-toggle");var i=document.getElementById(e);if(i){var n=s.getInstance("Drawer",e);if(n){var toggleDrawer=function(){n.toggle()};t.addEventListener("click",toggleDrawer);n.addEventListenerInstance(t,"click",toggleDrawer)}else console.error("Drawer with id ".concat(e," has not been initialized. Please initialize it using the data-drawer-target attribute."))}else console.error("Drawer with id ".concat(e," not found. Are you sure that the data-drawer-target attribute points to the correct drawer id?"))}));document.querySelectorAll("[data-drawer-dismiss], [data-drawer-hide]").forEach((function(t){var e=t.getAttribute("data-drawer-dismiss")?t.getAttribute("data-drawer-dismiss"):t.getAttribute("data-drawer-hide");var i=document.getElementById(e);if(i){var n=s.getInstance("Drawer",e);if(n){var hideDrawer=function(){n.hide()};t.addEventListener("click",hideDrawer);n.addEventListenerInstance(t,"click",hideDrawer)}else console.error("Drawer with id ".concat(e," has not been initialized. Please initialize it using the data-drawer-target attribute."))}else console.error("Drawer with id ".concat(e," not found. Are you sure that the data-drawer-target attribute points to the correct drawer id"))}));document.querySelectorAll("[data-drawer-show]").forEach((function(t){var e=t.getAttribute("data-drawer-show");var i=document.getElementById(e);if(i){var n=s.getInstance("Drawer",e);if(n){var showDrawer=function(){n.show()};t.addEventListener("click",showDrawer);n.addEventListenerInstance(t,"click",showDrawer)}else console.error("Drawer with id ".concat(e," has not been initialized. Please initialize it using the data-drawer-target attribute."))}else console.error("Drawer with id ".concat(e," not found. Are you sure that the data-drawer-target attribute points to the correct drawer id?"))}))}if(typeof window!=="undefined"){window.Drawer=P;window.initDrawers=initDrawers}var z=(void 0,function(){z=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return z.apply(this,arguments)});var M={defaultTabId:null,activeClasses:"text-blue-600 hover:text-blue-600 dark:text-blue-500 dark:hover:text-blue-500 border-blue-600 dark:border-blue-500",inactiveClasses:"dark:border-transparent text-gray-500 hover:text-gray-600 dark:text-gray-400 border-gray-100 hover:border-gray-300 dark:border-gray-700 dark:hover:text-gray-300",onShow:function(){}};var j={id:null,override:true};var q=function(){function Tabs(t,e,i,n){t===void 0&&(t=null);e===void 0&&(e=[]);i===void 0&&(i=M);n===void 0&&(n=j);this._instanceId=n.id?n.id:t.id;this._tabsEl=t;this._items=e;this._activeTab=i?this.getTab(i.defaultTabId):null;this._options=z(z({},M),i);this._initialized=false;this.init();s.addInstance("Tabs",this,this._instanceId,n.override)}Tabs.prototype.init=function(){var t=this;if(this._items.length&&!this._initialized){this._activeTab||this.setActiveTab(this._items[0]);this.show(this._activeTab.id,true);this._items.map((function(e){e.triggerEl.addEventListener("click",(function(i){i.preventDefault();t.show(e.id)}))}))}};Tabs.prototype.destroy=function(){this._initialized&&(this._initialized=false)};Tabs.prototype.removeInstance=function(){this.destroy();s.removeInstance("Tabs",this._instanceId)};Tabs.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Tabs.prototype.getActiveTab=function(){return this._activeTab};Tabs.prototype.setActiveTab=function(t){this._activeTab=t};Tabs.prototype.getTab=function(t){return this._items.filter((function(e){return e.id===t}))[0]};Tabs.prototype.show=function(t,e){var i,n;var o=this;e===void 0&&(e=false);var s=this.getTab(t);if(s!==this._activeTab||e){this._items.map((function(t){var e,i;if(t!==s){(e=t.triggerEl.classList).remove.apply(e,o._options.activeClasses.split(" "));(i=t.triggerEl.classList).add.apply(i,o._options.inactiveClasses.split(" "));t.targetEl.classList.add("hidden");t.triggerEl.setAttribute("aria-selected","false")}}));(i=s.triggerEl.classList).add.apply(i,this._options.activeClasses.split(" "));(n=s.triggerEl.classList).remove.apply(n,this._options.inactiveClasses.split(" "));s.triggerEl.setAttribute("aria-selected","true");s.targetEl.classList.remove("hidden");this.setActiveTab(s);this._options.onShow(this,s)}};Tabs.prototype.updateOnShow=function(t){this._options.onShow=t};return Tabs}();function initTabs(){document.querySelectorAll("[data-tabs-toggle]").forEach((function(t){var e=[];var i=t.getAttribute("data-tabs-active-classes");var n=t.getAttribute("data-tabs-inactive-classes");var o=null;t.querySelectorAll('[role="tab"]').forEach((function(t){var i=t.getAttribute("aria-selected")==="true";var n={id:t.getAttribute("data-tabs-target"),triggerEl:t,targetEl:document.querySelector(t.getAttribute("data-tabs-target"))};e.push(n);i&&(o=n.id)}));new q(t,e,{defaultTabId:o,activeClasses:i||M.activeClasses,inactiveClasses:n||M.inactiveClasses})}))}if(typeof window!=="undefined"){window.Tabs=q;window.initTabs=initTabs}var V=(void 0,function(){V=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return V.apply(this,arguments)});var B=(void 0,function(t,e,i){if(i||arguments.length===2)for(var n,o=0,s=e.length;o<s;o++)if(n||!(o in e)){n||(n=Array.prototype.slice.call(e,0,o));n[o]=e[o]}return t.concat(n||Array.prototype.slice.call(e))});var R={placement:"top",triggerType:"hover",onShow:function(){},onHide:function(){},onToggle:function(){}};var F={id:null,override:true};var K=function(){function Tooltip(t,e,i,n){t===void 0&&(t=null);e===void 0&&(e=null);i===void 0&&(i=R);n===void 0&&(n=F);this._instanceId=n.id?n.id:t.id;this._targetEl=t;this._triggerEl=e;this._options=V(V({},R),i);this._popperInstance=null;this._visible=false;this._initialized=false;this.init();s.addInstance("Tooltip",this,this._instanceId,n.override)}Tooltip.prototype.init=function(){if(this._triggerEl&&this._targetEl&&!this._initialized){this._setupEventListeners();this._popperInstance=this._createPopperInstance();this._initialized=true}};Tooltip.prototype.destroy=function(){var t=this;if(this._initialized){var e=this._getTriggerEvents();e.showEvents.forEach((function(e){t._triggerEl.removeEventListener(e,t._showHandler)}));e.hideEvents.forEach((function(e){t._triggerEl.removeEventListener(e,t._hideHandler)}));this._removeKeydownListener();this._removeClickOutsideListener();this._popperInstance&&this._popperInstance.destroy();this._initialized=false}};Tooltip.prototype.removeInstance=function(){s.removeInstance("Tooltip",this._instanceId)};Tooltip.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Tooltip.prototype._setupEventListeners=function(){var t=this;var e=this._getTriggerEvents();this._showHandler=function(){t.show()};this._hideHandler=function(){t.hide()};e.showEvents.forEach((function(e){t._triggerEl.addEventListener(e,t._showHandler)}));e.hideEvents.forEach((function(e){t._triggerEl.addEventListener(e,t._hideHandler)}))};Tooltip.prototype._createPopperInstance=function(){return t(this._triggerEl,this._targetEl,{placement:this._options.placement,modifiers:[{name:"offset",options:{offset:[0,8]}}]})};Tooltip.prototype._getTriggerEvents=function(){switch(this._options.triggerType){case"hover":return{showEvents:["mouseenter","focus"],hideEvents:["mouseleave","blur"]};case"click":return{showEvents:["click","focus"],hideEvents:["focusout","blur"]};case"none":return{showEvents:[],hideEvents:[]};default:return{showEvents:["mouseenter","focus"],hideEvents:["mouseleave","blur"]}}};Tooltip.prototype._setupKeydownListener=function(){var t=this;this._keydownEventListener=function(e){e.key==="Escape"&&t.hide()};document.body.addEventListener("keydown",this._keydownEventListener,true)};Tooltip.prototype._removeKeydownListener=function(){document.body.removeEventListener("keydown",this._keydownEventListener,true)};Tooltip.prototype._setupClickOutsideListener=function(){var t=this;this._clickOutsideEventListener=function(e){t._handleClickOutside(e,t._targetEl)};document.body.addEventListener("click",this._clickOutsideEventListener,true)};Tooltip.prototype._removeClickOutsideListener=function(){document.body.removeEventListener("click",this._clickOutsideEventListener,true)};Tooltip.prototype._handleClickOutside=function(t,e){var i=t.target;i===e||e.contains(i)||this._triggerEl.contains(i)||!this.isVisible()||this.hide()};Tooltip.prototype.isVisible=function(){return this._visible};Tooltip.prototype.toggle=function(){this.isVisible()?this.hide():this.show()};Tooltip.prototype.show=function(){this._targetEl.classList.remove("opacity-0","invisible");this._targetEl.classList.add("opacity-100","visible");this._popperInstance.setOptions((function(t){return V(V({},t),{modifiers:B(B([],t.modifiers,true),[{name:"eventListeners",enabled:true}],false)})}));this._setupClickOutsideListener();this._setupKeydownListener();this._popperInstance.update();this._visible=true;this._options.onShow(this)};Tooltip.prototype.hide=function(){this._targetEl.classList.remove("opacity-100","visible");this._targetEl.classList.add("opacity-0","invisible");this._popperInstance.setOptions((function(t){return V(V({},t),{modifiers:B(B([],t.modifiers,true),[{name:"eventListeners",enabled:false}],false)})}));this._removeClickOutsideListener();this._removeKeydownListener();this._visible=false;this._options.onHide(this)};Tooltip.prototype.updateOnShow=function(t){this._options.onShow=t};Tooltip.prototype.updateOnHide=function(t){this._options.onHide=t};Tooltip.prototype.updateOnToggle=function(t){this._options.onToggle=t};return Tooltip}();function initTooltips(){document.querySelectorAll("[data-tooltip-target]").forEach((function(t){var e=t.getAttribute("data-tooltip-target");var i=document.getElementById(e);if(i){var n=t.getAttribute("data-tooltip-trigger");var o=t.getAttribute("data-tooltip-placement");new K(i,t,{placement:o||R.placement,triggerType:n||R.triggerType})}else console.error('The tooltip element with id "'.concat(e,'" does not exist. Please check the data-tooltip-target attribute.'))}))}if(typeof window!=="undefined"){window.Tooltip=K;window.initTooltips=initTooltips}var N=(void 0,function(){N=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return N.apply(this,arguments)});var $=(void 0,function(t,e,i){if(i||arguments.length===2)for(var n,o=0,s=e.length;o<s;o++)if(n||!(o in e)){n||(n=Array.prototype.slice.call(e,0,o));n[o]=e[o]}return t.concat(n||Array.prototype.slice.call(e))});var G={placement:"top",offset:10,triggerType:"hover",onShow:function(){},onHide:function(){},onToggle:function(){}};var J={id:null,override:true};var Q=function(){function Popover(t,e,i,n){t===void 0&&(t=null);e===void 0&&(e=null);i===void 0&&(i=G);n===void 0&&(n=J);this._instanceId=n.id?n.id:t.id;this._targetEl=t;this._triggerEl=e;this._options=N(N({},G),i);this._popperInstance=null;this._visible=false;this._initialized=false;this.init();s.addInstance("Popover",this,n.id?n.id:this._targetEl.id,n.override)}Popover.prototype.init=function(){if(this._triggerEl&&this._targetEl&&!this._initialized){this._setupEventListeners();this._popperInstance=this._createPopperInstance();this._initialized=true}};Popover.prototype.destroy=function(){var t=this;if(this._initialized){var e=this._getTriggerEvents();e.showEvents.forEach((function(e){t._triggerEl.removeEventListener(e,t._showHandler);t._targetEl.removeEventListener(e,t._showHandler)}));e.hideEvents.forEach((function(e){t._triggerEl.removeEventListener(e,t._hideHandler);t._targetEl.removeEventListener(e,t._hideHandler)}));this._removeKeydownListener();this._removeClickOutsideListener();this._popperInstance&&this._popperInstance.destroy();this._initialized=false}};Popover.prototype.removeInstance=function(){s.removeInstance("Popover",this._instanceId)};Popover.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Popover.prototype._setupEventListeners=function(){var t=this;var e=this._getTriggerEvents();this._showHandler=function(){t.show()};this._hideHandler=function(){setTimeout((function(){t._targetEl.matches(":hover")||t.hide()}),100)};e.showEvents.forEach((function(e){t._triggerEl.addEventListener(e,t._showHandler);t._targetEl.addEventListener(e,t._showHandler)}));e.hideEvents.forEach((function(e){t._triggerEl.addEventListener(e,t._hideHandler);t._targetEl.addEventListener(e,t._hideHandler)}))};Popover.prototype._createPopperInstance=function(){return t(this._triggerEl,this._targetEl,{placement:this._options.placement,modifiers:[{name:"offset",options:{offset:[0,this._options.offset]}}]})};Popover.prototype._getTriggerEvents=function(){switch(this._options.triggerType){case"hover":return{showEvents:["mouseenter","focus"],hideEvents:["mouseleave","blur"]};case"click":return{showEvents:["click","focus"],hideEvents:["focusout","blur"]};case"none":return{showEvents:[],hideEvents:[]};default:return{showEvents:["mouseenter","focus"],hideEvents:["mouseleave","blur"]}}};Popover.prototype._setupKeydownListener=function(){var t=this;this._keydownEventListener=function(e){e.key==="Escape"&&t.hide()};document.body.addEventListener("keydown",this._keydownEventListener,true)};Popover.prototype._removeKeydownListener=function(){document.body.removeEventListener("keydown",this._keydownEventListener,true)};Popover.prototype._setupClickOutsideListener=function(){var t=this;this._clickOutsideEventListener=function(e){t._handleClickOutside(e,t._targetEl)};document.body.addEventListener("click",this._clickOutsideEventListener,true)};Popover.prototype._removeClickOutsideListener=function(){document.body.removeEventListener("click",this._clickOutsideEventListener,true)};Popover.prototype._handleClickOutside=function(t,e){var i=t.target;i===e||e.contains(i)||this._triggerEl.contains(i)||!this.isVisible()||this.hide()};Popover.prototype.isVisible=function(){return this._visible};Popover.prototype.toggle=function(){this.isVisible()?this.hide():this.show();this._options.onToggle(this)};Popover.prototype.show=function(){this._targetEl.classList.remove("opacity-0","invisible");this._targetEl.classList.add("opacity-100","visible");this._popperInstance.setOptions((function(t){return N(N({},t),{modifiers:$($([],t.modifiers,true),[{name:"eventListeners",enabled:true}],false)})}));this._setupClickOutsideListener();this._setupKeydownListener();this._popperInstance.update();this._visible=true;this._options.onShow(this)};Popover.prototype.hide=function(){this._targetEl.classList.remove("opacity-100","visible");this._targetEl.classList.add("opacity-0","invisible");this._popperInstance.setOptions((function(t){return N(N({},t),{modifiers:$($([],t.modifiers,true),[{name:"eventListeners",enabled:false}],false)})}));this._removeClickOutsideListener();this._removeKeydownListener();this._visible=false;this._options.onHide(this)};Popover.prototype.updateOnShow=function(t){this._options.onShow=t};Popover.prototype.updateOnHide=function(t){this._options.onHide=t};Popover.prototype.updateOnToggle=function(t){this._options.onToggle=t};return Popover}();function initPopovers(){document.querySelectorAll("[data-popover-target]").forEach((function(t){var e=t.getAttribute("data-popover-target");var i=document.getElementById(e);if(i){var n=t.getAttribute("data-popover-trigger");var o=t.getAttribute("data-popover-placement");var s=t.getAttribute("data-popover-offset");new Q(i,t,{placement:o||G.placement,offset:s?parseInt(s):G.offset,triggerType:n||G.triggerType})}else console.error('The popover element with id "'.concat(e,'" does not exist. Please check the data-popover-target attribute.'))}))}if(typeof window!=="undefined"){window.Popover=Q;window.initPopovers=initPopovers}var U=(void 0,function(){U=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return U.apply(this,arguments)});var W={triggerType:"hover",onShow:function(){},onHide:function(){},onToggle:function(){}};var X={id:null,override:true};var Y=function(){function Dial(t,e,i,n,o){t===void 0&&(t=null);e===void 0&&(e=null);i===void 0&&(i=null);n===void 0&&(n=W);o===void 0&&(o=X);this._instanceId=o.id?o.id:i.id;this._parentEl=t;this._triggerEl=e;this._targetEl=i;this._options=U(U({},W),n);this._visible=false;this._initialized=false;this.init();s.addInstance("Dial",this,this._instanceId,o.override)}Dial.prototype.init=function(){var t=this;if(this._triggerEl&&this._targetEl&&!this._initialized){var e=this._getTriggerEventTypes(this._options.triggerType);this._showEventHandler=function(){t.show()};e.showEvents.forEach((function(e){t._triggerEl.addEventListener(e,t._showEventHandler);t._targetEl.addEventListener(e,t._showEventHandler)}));this._hideEventHandler=function(){t._parentEl.matches(":hover")||t.hide()};e.hideEvents.forEach((function(e){t._parentEl.addEventListener(e,t._hideEventHandler)}));this._initialized=true}};Dial.prototype.destroy=function(){var t=this;if(this._initialized){var e=this._getTriggerEventTypes(this._options.triggerType);e.showEvents.forEach((function(e){t._triggerEl.removeEventListener(e,t._showEventHandler);t._targetEl.removeEventListener(e,t._showEventHandler)}));e.hideEvents.forEach((function(e){t._parentEl.removeEventListener(e,t._hideEventHandler)}));this._initialized=false}};Dial.prototype.removeInstance=function(){s.removeInstance("Dial",this._instanceId)};Dial.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Dial.prototype.hide=function(){this._targetEl.classList.add("hidden");this._triggerEl&&this._triggerEl.setAttribute("aria-expanded","false");this._visible=false;this._options.onHide(this)};Dial.prototype.show=function(){this._targetEl.classList.remove("hidden");this._triggerEl&&this._triggerEl.setAttribute("aria-expanded","true");this._visible=true;this._options.onShow(this)};Dial.prototype.toggle=function(){this._visible?this.hide():this.show()};Dial.prototype.isHidden=function(){return!this._visible};Dial.prototype.isVisible=function(){return this._visible};Dial.prototype._getTriggerEventTypes=function(t){switch(t){case"hover":return{showEvents:["mouseenter","focus"],hideEvents:["mouseleave","blur"]};case"click":return{showEvents:["click","focus"],hideEvents:["focusout","blur"]};case"none":return{showEvents:[],hideEvents:[]};default:return{showEvents:["mouseenter","focus"],hideEvents:["mouseleave","blur"]}}};Dial.prototype.updateOnShow=function(t){this._options.onShow=t};Dial.prototype.updateOnHide=function(t){this._options.onHide=t};Dial.prototype.updateOnToggle=function(t){this._options.onToggle=t};return Dial}();function initDials(){document.querySelectorAll("[data-dial-init]").forEach((function(t){var e=t.querySelector("[data-dial-toggle]");if(e){var i=e.getAttribute("data-dial-toggle");var n=document.getElementById(i);if(n){var o=e.getAttribute("data-dial-trigger");new Y(t,e,n,{triggerType:o||W.triggerType})}else console.error("Dial with id ".concat(i," does not exist. Are you sure that the data-dial-toggle attribute points to the correct modal id?"))}else console.error("Dial with id ".concat(t.id," does not have a trigger element. Are you sure that the data-dial-toggle attribute exists?"))}))}if(typeof window!=="undefined"){window.Dial=Y;window.initDials=initDials}var Z=(void 0,function(){Z=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return Z.apply(this,arguments)});var tt={minValue:null,maxValue:null,onIncrement:function(){},onDecrement:function(){}};var et={id:null,override:true};var it=function(){function InputCounter(t,e,i,n,o){t===void 0&&(t=null);e===void 0&&(e=null);i===void 0&&(i=null);n===void 0&&(n=tt);o===void 0&&(o=et);this._instanceId=o.id?o.id:t.id;this._targetEl=t;this._incrementEl=e;this._decrementEl=i;this._options=Z(Z({},tt),n);this._initialized=false;this.init();s.addInstance("InputCounter",this,this._instanceId,o.override)}InputCounter.prototype.init=function(){var t=this;if(this._targetEl&&!this._initialized){this._inputHandler=function(e){var i=e.target;/^\d*$/.test(i.value)||(i.value=i.value.replace(/[^\d]/g,""));t._options.maxValue!==null&&parseInt(i.value)>t._options.maxValue&&(i.value=t._options.maxValue.toString());t._options.minValue!==null&&parseInt(i.value)<t._options.minValue&&(i.value=t._options.minValue.toString())};this._incrementClickHandler=function(){t.increment()};this._decrementClickHandler=function(){t.decrement()};this._targetEl.addEventListener("input",this._inputHandler);this._incrementEl&&this._incrementEl.addEventListener("click",this._incrementClickHandler);this._decrementEl&&this._decrementEl.addEventListener("click",this._decrementClickHandler);this._initialized=true}};InputCounter.prototype.destroy=function(){if(this._targetEl&&this._initialized){this._targetEl.removeEventListener("input",this._inputHandler);this._incrementEl&&this._incrementEl.removeEventListener("click",this._incrementClickHandler);this._decrementEl&&this._decrementEl.removeEventListener("click",this._decrementClickHandler);this._initialized=false}};InputCounter.prototype.removeInstance=function(){s.removeInstance("InputCounter",this._instanceId)};InputCounter.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};InputCounter.prototype.getCurrentValue=function(){return parseInt(this._targetEl.value)||0};InputCounter.prototype.increment=function(){if(!(this._options.maxValue!==null&&this.getCurrentValue()>=this._options.maxValue)){this._targetEl.value=(this.getCurrentValue()+1).toString();this._options.onIncrement(this)}};InputCounter.prototype.decrement=function(){if(!(this._options.minValue!==null&&this.getCurrentValue()<=this._options.minValue)){this._targetEl.value=(this.getCurrentValue()-1).toString();this._options.onDecrement(this)}};InputCounter.prototype.updateOnIncrement=function(t){this._options.onIncrement=t};InputCounter.prototype.updateOnDecrement=function(t){this._options.onDecrement=t};return InputCounter}();function initInputCounters(){document.querySelectorAll("[data-input-counter]").forEach((function(t){var e=t.id;var i=document.querySelector('[data-input-counter-increment="'+e+'"]');var n=document.querySelector('[data-input-counter-decrement="'+e+'"]');var o=t.getAttribute("data-input-counter-min");var r=t.getAttribute("data-input-counter-max");t?s.instanceExists("InputCounter",t.getAttribute("id"))||new it(t,i||null,n||null,{minValue:o?parseInt(o):null,maxValue:r?parseInt(r):null}):console.error('The target element with id "'.concat(e,'" does not exist. Please check the data-input-counter attribute.'))}))}if(typeof window!=="undefined"){window.InputCounter=it;window.initInputCounters=initInputCounters}var nt=(void 0,function(){nt=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return nt.apply(this,arguments)});var ot={htmlEntities:false,contentType:"input",onCopy:function(){}};var st={id:null,override:true};var rt=function(){function CopyClipboard(t,e,i,n){t===void 0&&(t=null);e===void 0&&(e=null);i===void 0&&(i=ot);n===void 0&&(n=st);this._instanceId=n.id?n.id:e.id;this._triggerEl=t;this._targetEl=e;this._options=nt(nt({},ot),i);this._initialized=false;this.init();s.addInstance("CopyClipboard",this,this._instanceId,n.override)}CopyClipboard.prototype.init=function(){var t=this;if(this._targetEl&&this._triggerEl&&!this._initialized){this._triggerElClickHandler=function(){t.copy()};this._triggerEl&&this._triggerEl.addEventListener("click",this._triggerElClickHandler);this._initialized=true}};CopyClipboard.prototype.destroy=function(){if(this._triggerEl&&this._targetEl&&this._initialized){this._triggerEl&&this._triggerEl.removeEventListener("click",this._triggerElClickHandler);this._initialized=false}};CopyClipboard.prototype.removeInstance=function(){s.removeInstance("CopyClipboard",this._instanceId)};CopyClipboard.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};CopyClipboard.prototype.getTargetValue=function(){return this._options.contentType==="input"?this._targetEl.value:this._options.contentType==="innerHTML"?this._targetEl.innerHTML:this._options.contentType==="textContent"?this._targetEl.textContent.replace(/\s+/g," ").trim():void 0};CopyClipboard.prototype.copy=function(){var t=this.getTargetValue();this._options.htmlEntities&&(t=this.decodeHTML(t));var e=document.createElement("textarea");e.value=t;document.body.appendChild(e);e.select();document.execCommand("copy");document.body.removeChild(e);this._options.onCopy(this);return t};CopyClipboard.prototype.decodeHTML=function(t){var e=document.createElement("textarea");e.innerHTML=t;return e.textContent};CopyClipboard.prototype.updateOnCopyCallback=function(t){this._options.onCopy=t};return CopyClipboard}();function initCopyClipboards(){document.querySelectorAll("[data-copy-to-clipboard-target]").forEach((function(t){var e=t.getAttribute("data-copy-to-clipboard-target");var i=document.getElementById(e);var n=t.getAttribute("data-copy-to-clipboard-content-type");var o=t.getAttribute("data-copy-to-clipboard-html-entities");i?s.instanceExists("CopyClipboard",i.getAttribute("id"))||new rt(t,i,{htmlEntities:!(!o||o!=="true")||ot.htmlEntities,contentType:n||ot.contentType}):console.error('The target element with id "'.concat(e,'" does not exist. Please check the data-copy-to-clipboard-target attribute.'))}))}if(typeof window!=="undefined"){window.CopyClipboard=rt;window.initClipboards=initCopyClipboards}var at=(void 0,function(){at=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++){e=arguments[i];for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])}return t};return at.apply(this,arguments)});var lt={defaultDatepickerId:null,autohide:false,format:"mm/dd/yyyy",maxDate:null,minDate:null,orientation:"bottom",buttons:false,autoSelectToday:0,title:null,language:"en",rangePicker:false,onShow:function(){},onHide:function(){}};var dt={id:null,override:true};var ct=function(){function Datepicker(t,e,i){t===void 0&&(t=null);e===void 0&&(e=lt);i===void 0&&(i=dt);this._instanceId=i.id?i.id:t.id;this._datepickerEl=t;this._datepickerInstance=null;this._options=at(at({},lt),e);this._initialized=false;this.init();s.addInstance("Datepicker",this,this._instanceId,i.override)}Datepicker.prototype.init=function(){if(this._datepickerEl&&!this._initialized){this._options.rangePicker?this._datepickerInstance=new e(this._datepickerEl,this._getDatepickerOptions(this._options)):this._datepickerInstance=new i(this._datepickerEl,this._getDatepickerOptions(this._options));this._initialized=true}};Datepicker.prototype.destroy=function(){if(this._initialized){this._initialized=false;this._datepickerInstance.destroy()}};Datepicker.prototype.removeInstance=function(){this.destroy();s.removeInstance("Datepicker",this._instanceId)};Datepicker.prototype.destroyAndRemoveInstance=function(){this.destroy();this.removeInstance()};Datepicker.prototype.getDatepickerInstance=function(){return this._datepickerInstance};Datepicker.prototype.getDate=function(){return this._options.rangePicker&&this._datepickerInstance instanceof e?this._datepickerInstance.getDates():!this._options.rangePicker&&this._datepickerInstance instanceof i?this._datepickerInstance.getDate():void 0};Datepicker.prototype.setDate=function(t){return this._options.rangePicker&&this._datepickerInstance instanceof e?this._datepickerInstance.setDates(t):!this._options.rangePicker&&this._datepickerInstance instanceof i?this._datepickerInstance.setDate(t):void 0};Datepicker.prototype.show=function(){this._datepickerInstance.show();this._options.onShow(this)};Datepicker.prototype.hide=function(){this._datepickerInstance.hide();this._options.onHide(this)};Datepicker.prototype._getDatepickerOptions=function(t){var e={};if(t.buttons){e.todayBtn=true;e.clearBtn=true;t.autoSelectToday&&(e.todayBtnMode=1)}t.autohide&&(e.autohide=true);t.format&&(e.format=t.format);t.maxDate&&(e.maxDate=t.maxDate);t.minDate&&(e.minDate=t.minDate);t.orientation&&(e.orientation=t.orientation);t.title&&(e.title=t.title);t.language&&(e.language=t.language);return e};Datepicker.prototype.updateOnShow=function(t){this._options.onShow=t};Datepicker.prototype.updateOnHide=function(t){this._options.onHide=t};return Datepicker}();function initDatepickers(){document.querySelectorAll("[datepicker], [inline-datepicker], [date-rangepicker]").forEach((function(t){if(t){var e=t.hasAttribute("datepicker-buttons");var i=t.hasAttribute("datepicker-autoselect-today");var n=t.hasAttribute("datepicker-autohide");var o=t.getAttribute("datepicker-format");var s=t.getAttribute("datepicker-max-date");var r=t.getAttribute("datepicker-min-date");var a=t.getAttribute("datepicker-orientation");var l=t.getAttribute("datepicker-title");var d=t.getAttribute("datepicker-language");var c=t.hasAttribute("date-rangepicker");new ct(t,{buttons:e||lt.buttons,autoSelectToday:i||lt.autoSelectToday,autohide:n||lt.autohide,format:o||lt.format,maxDate:s||lt.maxDate,minDate:r||lt.minDate,orientation:a||lt.orientation,title:l||lt.title,language:d||lt.language,rangePicker:c||lt.rangePicker})}else console.error("The datepicker element does not exist. Please check the datepicker attribute.")}))}if(typeof window!=="undefined"){window.Datepicker=ct;window.initDatepickers=initDatepickers}function initFlowbite(){initAccordions();initCollapses();initCarousels();initDismisses();initDropdowns();initModals();initDrawers();initTabs();initTooltips();initPopovers();initDials();initInputCounters();initCopyClipboards();initDatepickers()}typeof window!=="undefined"&&(window.initFlowbite=initFlowbite);var pt=new n("load",[initAccordions,initCollapses,initCarousels,initDismisses,initDropdowns,initModals,initDrawers,initTabs,initTooltips,initPopovers,initDials,initInputCounters,initCopyClipboards,initDatepickers]);pt.init();export{d as Accordion,_ as Carousel,u as Collapse,rt as CopyClipboard,ct as Datepicker,Y as Dial,w as Dismiss,P as Drawer,L as Dropdown,it as InputCounter,O as Modal,Q as Popover,q as Tabs,K as Tooltip,initAccordions,initCarousels,initCollapses,initCopyClipboards,initDatepickers,initDials,initDismisses,initDrawers,initDropdowns,initFlowbite,initInputCounters,initModals,initPopovers,initTabs,initTooltips};

