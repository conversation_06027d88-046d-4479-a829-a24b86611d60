// flowbite-datepicker@1.3.2 downloaded from https://ga.jspm.io/npm:flowbite-datepicker@1.3.2/dist/main.esm.js

function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,a=Array(t);r<t;r++)a[r]=e[r];return a}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _callSuper(e,t,r){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],_getPrototypeOf(e).constructor):t.apply(e,r))}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,_toPropertyKey(a.key),a)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _get(){return _get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,r){var a=_superPropBase(e,t);if(a){var i=Object.getOwnPropertyDescriptor(a,t);return i.get?i.get.call(arguments.length<3?e:r):i.value}},_get.apply(null,arguments)}function _getPrototypeOf(e){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_getPrototypeOf(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var a,i,n,s,o=[],l=!0,c=!1;try{if(n=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(a=n.call(r)).done)&&(o.push(a.value),o.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(c)throw i}}return o}}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _possibleConstructorReturn(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _setPrototypeOf(e,t){return _setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},_setPrototypeOf(e,t)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _superPropBase(e,t){for(;!{}.hasOwnProperty.call(e,t)&&null!==(e=_getPrototypeOf(e)););return e}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _toPrimitive(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}function _toPropertyKey(e){var t=_toPrimitive(e,"string");return"symbol"==typeof t?t:t+""}function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}function _unsupportedIterableToArray(e,t){if(e){if("string"==typeof e)return _arrayLikeToArray(e,t);var r={}.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}}function hasProperty(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function lastItemOf(e){return e[e.length-1]}function pushUnique(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];r.forEach((function(t){e.includes(t)||e.push(t)}));return e}function stringToArray(e,t){return e?e.split(t):[]}function isInRange(e,t,r){var a=t===void 0||e>=t;var i=r===void 0||e<=r;return a&&i}function limitToRange(e,t,r){return e<t?t:e>r?r:e}function createTagRepeat(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;var i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:"";var n=Object.keys(r).reduce((function(e,t){var i=r[t];typeof i==="function"&&(i=i(a));return"".concat(e," ").concat(t,'="').concat(i,'"')}),e);i+="<".concat(n,"></").concat(e,">");var s=a+1;return s<t?createTagRepeat(e,t,r,s,i):i}function optimizeTemplateHTML(e){return e.replace(/>\s+/g,">").replace(/\s+</,"<")}function stripTime(e){return new Date(e).setHours(0,0,0,0)}function today(){return(new Date).setHours(0,0,0,0)}function dateValue(){switch(arguments.length){case 0:return today();case 1:return stripTime(arguments.length<=0?void 0:arguments[0])}var e=new Date(0);e.setFullYear.apply(e,arguments);return e.setHours(0,0,0,0)}function addDays(e,t){var r=new Date(e);return r.setDate(r.getDate()+t)}function addWeeks(e,t){return addDays(e,t*7)}function addMonths(e,t){var r=new Date(e);var a=r.getMonth()+t;var i=a%12;i<0&&(i+=12);var n=r.setMonth(a);return r.getMonth()!==i?r.setDate(0):n}function addYears(e,t){var r=new Date(e);var a=r.getMonth();var i=r.setFullYear(r.getFullYear()+t);return a===1&&r.getMonth()===2?r.setDate(0):i}function dayDiff(e,t){return(e-t+7)%7}function dayOfTheWeekOf(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;var a=new Date(e).getDay();return addDays(e,dayDiff(t,r)-dayDiff(a,r))}function getWeek(e){var t=dayOfTheWeekOf(e,4,1);var r=dayOfTheWeekOf(new Date(t).setMonth(0,4),4,1);return Math.round((t-r)/6048e5)+1}function startOfYearPeriod(e,t){var r=new Date(e).getFullYear();return Math.floor(r/t)*t}var e=/dd?|DD?|mm?|MM?|yy?(?:yy)?/;var t=/[\s!-/:-@[-`{-~年月日]+/;var r={};var a={y:function y(e,t){return new Date(e).setFullYear(parseInt(t,10))},m:function m(e,t,r){var a=new Date(e);var i=parseInt(t,10)-1;if(isNaN(i)){if(!t)return NaN;var n=t.toLowerCase();var s=function compareNames(e){return e.toLowerCase().startsWith(n)};i=r.monthsShort.findIndex(s);i<0&&(i=r.months.findIndex(s));if(i<0)return NaN}a.setMonth(i);return a.getMonth()!==normalizeMonth(i)?a.setDate(0):a.getTime()},d:function d(e,t){return new Date(e).setDate(parseInt(t,10))}};var i={d:function d(e){return e.getDate()},dd:function dd(e){return padZero(e.getDate(),2)},D:function D(e,t){return t.daysShort[e.getDay()]},DD:function DD(e,t){return t.days[e.getDay()]},m:function m(e){return e.getMonth()+1},mm:function mm(e){return padZero(e.getMonth()+1,2)},M:function M(e,t){return t.monthsShort[e.getMonth()]},MM:function MM(e,t){return t.months[e.getMonth()]},y:function y(e){return e.getFullYear()},yy:function yy(e){return padZero(e.getFullYear(),2).slice(-2)},yyyy:function yyyy(e){return padZero(e.getFullYear(),4)}};function normalizeMonth(e){return e>-1?e%12:normalizeMonth(e+12)}function padZero(e,t){return e.toString().padStart(t,"0")}function parseFormatString(n){if(typeof n!=="string")throw new Error("Invalid date format.");if(n in r)return r[n];var s=n.split(e);var o=n.match(new RegExp(e,"g"));if(s.length===0||!o)throw new Error("Invalid date format.");var l=o.map((function(e){return i[e]}));var c=Object.keys(a).reduce((function(e,t){var r=o.find((function(e){return e[0]!=="D"&&e[0].toLowerCase()===t}));r&&e.push(t);return e}),[]);return r[n]={parser:function parser(e,r){var i=e.split(t).reduce((function(e,t,r){if(t.length>0&&o[r]){var a=o[r][0];a==="M"?e.m=t:a!=="D"&&(e[a]=t)}return e}),{});return c.reduce((function(e,t){var n=a[t](e,i[t],r);return isNaN(n)?e:n}),today())},formatter:function formatter(e,t){var r=l.reduce((function(r,a,i){return r+"".concat(s[i]).concat(a(e,t))}),"");return r+lastItemOf(s)}}}function parseDate(e,t,r){if(e instanceof Date||typeof e==="number"){var a=stripTime(e);return isNaN(a)?void 0:a}if(e){if(e==="today")return today();if(t&&t.toValue){var i=t.toValue(e,t,r);return isNaN(i)?void 0:stripTime(i)}return parseFormatString(t).parser(e,r)}}function formatDate(e,t,r){if(isNaN(e)||!e&&e!==0)return"";var a=typeof e==="number"?new Date(e):e;return t.toDisplay?t.toDisplay(a,t,r):parseFormatString(t).formatter(a,r)}var n=new WeakMap;var s=EventTarget.prototype,o=s.addEventListener,l=s.removeEventListener;function registerListeners(e,t){var r=n.get(e);if(!r){r=[];n.set(e,r)}t.forEach((function(e){o.call.apply(o,_toConsumableArray(e));r.push(e)}))}function unregisterListeners(e){var t=n.get(e);if(t){t.forEach((function(e){l.call.apply(l,_toConsumableArray(e))}));n.delete(e)}}if(!Event.prototype.composedPath){var c=function getComposedPath(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[];t.push(e);var r;e.parentNode?r=e.parentNode:e.host?r=e.host:e.defaultView&&(r=e.defaultView);return r?getComposedPath(r,t):t};Event.prototype.composedPath=function(){return c(this.target)}}function findFromPath(e,t,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0;var i=e[a];return t(i)?i:i!==r&&i.parentElement?findFromPath(e,t,r,a+1):void 0}function findElementInEventPath(e,t){var r=typeof t==="function"?t:function(e){return e.matches(t)};return findFromPath(e.composedPath(),r,e.currentTarget)}var u={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today",clear:"Clear",titleFormat:"MM y"}};var h={autohide:false,beforeShowDay:null,beforeShowDecade:null,beforeShowMonth:null,beforeShowYear:null,calendarWeeks:false,clearBtn:false,dateDelimiter:",",datesDisabled:[],daysOfWeekDisabled:[],daysOfWeekHighlighted:[],defaultViewDate:void 0,disableTouchKeyboard:false,format:"mm/dd/yyyy",language:"en",maxDate:null,maxNumberOfDates:1,maxView:3,minDate:null,nextArrow:'<svg class="w-4 h-4 rtl:rotate-180 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/></svg>',orientation:"auto",pickLevel:0,prevArrow:'<svg class="w-4 h-4 rtl:rotate-180 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10"><path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5H1m0 0 4 4M1 5l4-4"/></svg>',showDaysOfWeek:true,showOnClick:true,showOnFocus:true,startView:0,title:"",todayBtn:false,todayBtnMode:0,todayHighlight:false,updateOnBlur:true,weekStart:0};var f=null;function parseHTML(e){f==null&&(f=document.createRange());return f.createContextualFragment(e)}function hideElement(e){if(e.style.display!=="none"){e.style.display&&(e.dataset.styleDisplay=e.style.display);e.style.display="none"}}function showElement(e){if(e.style.display==="none")if(e.dataset.styleDisplay){e.style.display=e.dataset.styleDisplay;delete e.dataset.styleDisplay}else e.style.display=""}function emptyChildNodes(e){if(e.firstChild){e.removeChild(e.firstChild);emptyChildNodes(e)}}function replaceChildNodes(e,t){emptyChildNodes(e);t instanceof DocumentFragment?e.appendChild(t):typeof t==="string"?e.appendChild(parseHTML(t)):typeof t.forEach==="function"&&t.forEach((function(t){e.appendChild(t)}))}var g=h.language,v=h.format,p=h.weekStart;function sanitizeDOW(e,t){return e.length<6&&t>=0&&t<7?pushUnique(e,t):e}function calcEndOfWeek(e){return(e+6)%7}function validateDate(e,t,r,a){var i=parseDate(e,t,r);return i!==void 0?i:a}function validateViewId(e,t){var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:3;var a=parseInt(e,10);return a>=0&&a<=r?a:t}function processOptions(t,r){var a=Object.assign({},t);var i={};var n=r.constructor.locales;var s=r.config||{},o=s.format,l=s.language,c=s.locale,u=s.maxDate,f=s.maxView,k=s.minDate,b=s.pickLevel,w=s.startView,x=s.weekStart;if(a.language){var O;if(a.language!==l)if(n[a.language])O=a.language;else{O=a.language.split("-")[0];n[O]===void 0&&(O=false)}delete a.language;if(O){l=i.language=O;var C=c||n[g];c=Object.assign({format:v,weekStart:p},n[g]);l!==g&&Object.assign(c,n[l]);i.locale=c;o===C.format&&(o=i.format=c.format);if(x===C.weekStart){x=i.weekStart=c.weekStart;i.weekEnd=calcEndOfWeek(c.weekStart)}}}if(a.format){var _=typeof a.format.toDisplay==="function";var S=typeof a.format.toValue==="function";var T=e.test(a.format);(_&&S||T)&&(o=i.format=a.format);delete a.format}var V=k;var A=u;if(a.minDate!==void 0){V=a.minDate===null?dateValue(0,0,1):validateDate(a.minDate,o,c,V);delete a.minDate}if(a.maxDate!==void 0){A=a.maxDate===null?void 0:validateDate(a.maxDate,o,c,A);delete a.maxDate}if(A<V){k=i.minDate=A;u=i.maxDate=V}else{k!==V&&(k=i.minDate=V);u!==A&&(u=i.maxDate=A)}if(a.datesDisabled){i.datesDisabled=a.datesDisabled.reduce((function(e,t){var r=parseDate(t,o,c);return r!==void 0?pushUnique(e,r):e}),[]);delete a.datesDisabled}if(a.defaultViewDate!==void 0){var E=parseDate(a.defaultViewDate,o,c);E!==void 0&&(i.defaultViewDate=E);delete a.defaultViewDate}if(a.weekStart!==void 0){var P=Number(a.weekStart)%7;if(!isNaN(P)){x=i.weekStart=P;i.weekEnd=calcEndOfWeek(P)}delete a.weekStart}if(a.daysOfWeekDisabled){i.daysOfWeekDisabled=a.daysOfWeekDisabled.reduce(sanitizeDOW,[]);delete a.daysOfWeekDisabled}if(a.daysOfWeekHighlighted){i.daysOfWeekHighlighted=a.daysOfWeekHighlighted.reduce(sanitizeDOW,[]);delete a.daysOfWeekHighlighted}if(a.maxNumberOfDates!==void 0){var L=parseInt(a.maxNumberOfDates,10);if(L>=0){i.maxNumberOfDates=L;i.multidate=L!==1}delete a.maxNumberOfDates}if(a.dateDelimiter){i.dateDelimiter=String(a.dateDelimiter);delete a.dateDelimiter}var F=b;if(a.pickLevel!==void 0){F=validateViewId(a.pickLevel,2);delete a.pickLevel}F!==b&&(b=i.pickLevel=F);var N=f;if(a.maxView!==void 0){N=validateViewId(a.maxView,f);delete a.maxView}N=b>N?b:N;N!==f&&(f=i.maxView=N);var B=w;if(a.startView!==void 0){B=validateViewId(a.startView,B);delete a.startView}B<b?B=b:B>f&&(B=f);B!==w&&(i.startView=B);if(a.prevArrow){var Y=parseHTML(a.prevArrow);Y.childNodes.length>0&&(i.prevArrow=Y.childNodes);delete a.prevArrow}if(a.nextArrow){var W=parseHTML(a.nextArrow);W.childNodes.length>0&&(i.nextArrow=W.childNodes);delete a.nextArrow}if(a.disableTouchKeyboard!==void 0){i.disableTouchKeyboard="ontouchstart"in document&&!!a.disableTouchKeyboard;delete a.disableTouchKeyboard}if(a.orientation){var I=a.orientation.toLowerCase().split(/\s+/g);i.orientation={x:I.find((function(e){return e==="left"||e==="right"}))||"auto",y:I.find((function(e){return e==="top"||e==="bottom"}))||"auto"};delete a.orientation}if(a.todayBtnMode!==void 0){switch(a.todayBtnMode){case 0:case 1:i.todayBtnMode=a.todayBtnMode}delete a.todayBtnMode}Object.keys(a).forEach((function(e){a[e]!==void 0&&hasProperty(h,e)&&(i[e]=a[e])}));return i}var k=optimizeTemplateHTML('<div class="datepicker hidden">\n  <div class="datepicker-picker inline-block rounded-lg bg-white dark:bg-gray-700 shadow-lg p-4">\n    <div class="datepicker-header">\n      <div class="datepicker-title bg-white dark:bg-gray-700 dark:text-white px-2 py-3 text-center font-semibold"></div>\n      <div class="datepicker-controls flex justify-between mb-2">\n        <button type="button" class="bg-white dark:bg-gray-700 rounded-lg text-gray-500 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-white text-lg p-2.5 focus:outline-none focus:ring-2 focus:ring-gray-200 prev-btn"></button>\n        <button type="button" class="text-sm rounded-lg text-gray-900 dark:text-white bg-white dark:bg-gray-700 font-semibold py-2.5 px-5 hover:bg-gray-100 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-200 view-switch"></button>\n        <button type="button" class="bg-white dark:bg-gray-700 rounded-lg text-gray-500 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-white text-lg p-2.5 focus:outline-none focus:ring-2 focus:ring-gray-200 next-btn"></button>\n      </div>\n    </div>\n    <div class="datepicker-main p-1"></div>\n    <div class="datepicker-footer">\n      <div class="datepicker-controls flex space-x-2 rtl:space-x-reverse mt-2">\n        <button type="button" class="%buttonClass% today-btn text-white bg-blue-700 !bg-primary-700 dark:bg-blue-600 dark:!bg-primary-600 hover:bg-blue-800 hover:!bg-primary-800 dark:hover:bg-blue-700 dark:hover:!bg-primary-700 focus:ring-4 focus:ring-blue-300 focus:!ring-primary-300 font-medium rounded-lg text-sm px-5 py-2 text-center w-1/2"></button>\n        <button type="button" class="%buttonClass% clear-btn text-gray-900 dark:text-white bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 focus:ring-4 focus:ring-blue-300 focus:!ring-primary-300 font-medium rounded-lg text-sm px-5 py-2 text-center w-1/2"></button>\n      </div>\n    </div>\n  </div>\n</div>');var b=optimizeTemplateHTML('<div class="days">\n  <div class="days-of-week grid grid-cols-7 mb-1">'.concat(createTagRepeat("span",7,{class:"dow block flex-1 leading-9 border-0 rounded-lg cursor-default text-center text-gray-900 font-semibold text-sm"}),'</div>\n  <div class="datepicker-grid w-64 grid grid-cols-7">').concat(createTagRepeat("span",42,{class:"block flex-1 leading-9 border-0 rounded-lg cursor-default text-center text-gray-900 font-semibold text-sm h-6 leading-6 text-sm font-medium text-gray-500 dark:text-gray-400"}),"</div>\n</div>"));var w=optimizeTemplateHTML('<div class="calendar-weeks">\n  <div class="days-of-week flex"><span class="dow h-6 leading-6 text-sm font-medium text-gray-500 dark:text-gray-400"></span></div>\n  <div class="weeks">'.concat(createTagRepeat("span",6,{class:"week block flex-1 leading-9 border-0 rounded-lg cursor-default text-center text-gray-900 font-semibold text-sm"}),"</div>\n</div>"));var x=function(){function View(e,t){_classCallCheck(this,View);Object.assign(this,t,{picker:e,element:parseHTML('<div class="datepicker-view flex"></div>').firstChild,selected:[]});this.init(this.picker.datepicker.config)}return _createClass(View,[{key:"init",value:function init(e){e.pickLevel!==void 0&&(this.isMinView=this.id===e.pickLevel);this.setOptions(e);this.updateFocus();this.updateSelection()}},{key:"performBeforeHook",value:function performBeforeHook(e,t,r){var a=this.beforeShow(new Date(r));switch(_typeof(a)){case"boolean":a={enabled:a};break;case"string":a={classes:a}}if(a){if(a.enabled===false){e.classList.add("disabled");pushUnique(this.disabled,t)}if(a.classes){var i;var n=a.classes.split(/\s+/);(i=e.classList).add.apply(i,_toConsumableArray(n));n.includes("disabled")&&pushUnique(this.disabled,t)}a.content&&replaceChildNodes(e,a.content)}}}])}();var O=function(e){function DaysView(e){_classCallCheck(this,DaysView);return _callSuper(this,DaysView,[e,{id:0,name:"days",cellClass:"day"}])}_inherits(DaysView,e);return _createClass(DaysView,[{key:"init",value:function init(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];if(t){var r=parseHTML(b).firstChild;this.dow=r.firstChild;this.grid=r.lastChild;this.element.appendChild(r)}_get(_getPrototypeOf(DaysView.prototype),"init",this).call(this,e)}},{key:"setOptions",value:function setOptions(e){var t=this;var r;hasProperty(e,"minDate")&&(this.minDate=e.minDate);hasProperty(e,"maxDate")&&(this.maxDate=e.maxDate);e.datesDisabled&&(this.datesDisabled=e.datesDisabled);if(e.daysOfWeekDisabled){this.daysOfWeekDisabled=e.daysOfWeekDisabled;r=true}e.daysOfWeekHighlighted&&(this.daysOfWeekHighlighted=e.daysOfWeekHighlighted);e.todayHighlight!==void 0&&(this.todayHighlight=e.todayHighlight);if(e.weekStart!==void 0){this.weekStart=e.weekStart;this.weekEnd=e.weekEnd;r=true}if(e.locale){var a=this.locale=e.locale;this.dayNames=a.daysMin;this.switchLabelFormat=a.titleFormat;r=true}e.beforeShowDay!==void 0&&(this.beforeShow=typeof e.beforeShowDay==="function"?e.beforeShowDay:void 0);if(e.calendarWeeks!==void 0)if(e.calendarWeeks&&!this.calendarWeeks){var i=parseHTML(w).firstChild;this.calendarWeeks={element:i,dow:i.firstChild,weeks:i.lastChild};this.element.insertBefore(i,this.element.firstChild)}else if(this.calendarWeeks&&!e.calendarWeeks){this.element.removeChild(this.calendarWeeks.element);this.calendarWeeks=null}if(e.showDaysOfWeek!==void 0)if(e.showDaysOfWeek){showElement(this.dow);this.calendarWeeks&&showElement(this.calendarWeeks.dow)}else{hideElement(this.dow);this.calendarWeeks&&hideElement(this.calendarWeeks.dow)}r&&Array.from(this.dow.children).forEach((function(e,r){var a=(t.weekStart+r)%7;e.textContent=t.dayNames[a];e.className=t.daysOfWeekDisabled.includes(a)?"dow disabled text-center h-6 leading-6 text-sm font-medium text-gray-500 dark:text-gray-400 cursor-not-allowed":"dow text-center h-6 leading-6 text-sm font-medium text-gray-500 dark:text-gray-400"}))}},{key:"updateFocus",value:function updateFocus(){var e=new Date(this.picker.viewDate);var t=e.getFullYear();var r=e.getMonth();var a=dateValue(t,r,1);var i=dayOfTheWeekOf(a,this.weekStart,this.weekStart);this.first=a;this.last=dateValue(t,r+1,0);this.start=i;this.focused=this.picker.viewDate}},{key:"updateSelection",value:function updateSelection(){var e=this.picker.datepicker,t=e.dates,r=e.rangepicker;this.selected=t;r&&(this.range=r.dates)}},{key:"render",value:function render(){var e=this;this.today=this.todayHighlight?today():void 0;this.disabled=_toConsumableArray(this.datesDisabled);var t=formatDate(this.focused,this.switchLabelFormat,this.locale);this.picker.setViewSwitchLabel(t);this.picker.setPrevBtnDisabled(this.first<=this.minDate);this.picker.setNextBtnDisabled(this.last>=this.maxDate);if(this.calendarWeeks){var r=dayOfTheWeekOf(this.first,1,1);Array.from(this.calendarWeeks.weeks.children).forEach((function(e,t){e.textContent=getWeek(addWeeks(r,t))}))}Array.from(this.grid.children).forEach((function(t,r){var a=t.classList;var i=addDays(e.start,r);var n=new Date(i);var s=n.getDay();t.className="datepicker-cell hover:bg-gray-100 dark:hover:bg-gray-600 block flex-1 leading-9 border-0 rounded-lg cursor-pointer text-center text-gray-900 dark:text-white font-semibold text-sm ".concat(e.cellClass);t.dataset.date=i;t.textContent=n.getDate();i<e.first?a.add("prev","text-gray-500","dark:text-white"):i>e.last&&a.add("next","text-gray-500","dark:text-white");e.today===i&&a.add("today","bg-gray-100","dark:bg-gray-600");if(i<e.minDate||i>e.maxDate||e.disabled.includes(i)){a.add("disabled","cursor-not-allowed","text-gray-400","dark:text-gray-500");a.remove("hover:bg-gray-100","dark:hover:bg-gray-600","text-gray-900","dark:text-white","cursor-pointer")}if(e.daysOfWeekDisabled.includes(s)){a.add("disabled","cursor-not-allowed","text-gray-400","dark:text-gray-500");a.remove("hover:bg-gray-100","dark:hover:bg-gray-600","text-gray-900","dark:text-white","cursor-pointer");pushUnique(e.disabled,i)}e.daysOfWeekHighlighted.includes(s)&&a.add("highlighted");if(e.range){var o=_slicedToArray(e.range,2),l=o[0],c=o[1];if(i>l&&i<c){a.add("range","bg-gray-200","dark:bg-gray-600");a.remove("rounded-lg","rounded-l-lg","rounded-r-lg")}if(i===l){a.add("range-start","bg-gray-100","dark:bg-gray-600","rounded-l-lg");a.remove("rounded-lg","rounded-r-lg")}if(i===c){a.add("range-end","bg-gray-100","dark:bg-gray-600","rounded-r-lg");a.remove("rounded-lg","rounded-l-lg")}}if(e.selected.includes(i)){a.add("selected","bg-blue-700","!bg-primary-700","text-white","dark:bg-blue-600","dark:!bg-primary-600","dark:text-white");a.remove("text-gray-900","text-gray-500","hover:bg-gray-100","dark:text-white","dark:hover:bg-gray-600","dark:bg-gray-600","bg-gray-100","bg-gray-200")}i===e.focused&&a.add("focused");e.beforeShow&&e.performBeforeHook(t,i,i)}))}},{key:"refresh",value:function refresh(){var e=this;var t=this.range||[],r=_slicedToArray(t,2),a=r[0],i=r[1];this.grid.querySelectorAll(".range, .range-start, .range-end, .selected, .focused").forEach((function(e){e.classList.remove("range","range-start","range-end","selected","bg-blue-700","!bg-primary-700","text-white","dark:bg-blue-600","dark:!bg-primary-600","dark:text-white","focused");e.classList.add("text-gray-900","rounded-lg","dark:text-white")}));Array.from(this.grid.children).forEach((function(t){var r=Number(t.dataset.date);var n=t.classList;n.remove("bg-gray-200","dark:bg-gray-600","rounded-l-lg","rounded-r-lg");if(r>a&&r<i){n.add("range","bg-gray-200","dark:bg-gray-600");n.remove("rounded-lg")}if(r===a){n.add("range-start","bg-gray-200","dark:bg-gray-600","rounded-l-lg");n.remove("rounded-lg")}if(r===i){n.add("range-end","bg-gray-200","dark:bg-gray-600","rounded-r-lg");n.remove("rounded-lg")}if(e.selected.includes(r)){n.add("selected","bg-blue-700","!bg-primary-700","text-white","dark:bg-blue-600","dark:!bg-primary-600","dark:text-white");n.remove("text-gray-900","hover:bg-gray-100","dark:text-white","dark:hover:bg-gray-600","bg-gray-100","bg-gray-200","dark:bg-gray-600")}r===e.focused&&n.add("focused")}))}},{key:"refreshFocus",value:function refreshFocus(){var e=Math.round((this.focused-this.start)/864e5);this.grid.querySelectorAll(".focused").forEach((function(e){e.classList.remove("focused")}));this.grid.children[e].classList.add("focused")}}])}(x);function computeMonthRange(e,t){if(e&&e[0]&&e[1]){var r=_slicedToArray(e,2),a=_slicedToArray(r[0],2),i=a[0],n=a[1],s=_slicedToArray(r[1],2),o=s[0],l=s[1];if(!(i>t||o<t))return[i===t?n:-1,o===t?l:12]}}var C=function(e){function MonthsView(e){_classCallCheck(this,MonthsView);return _callSuper(this,MonthsView,[e,{id:1,name:"months",cellClass:"month"}])}_inherits(MonthsView,e);return _createClass(MonthsView,[{key:"init",value:function init(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];if(t){this.grid=this.element;this.element.classList.add("months","datepicker-grid","w-64","grid","grid-cols-4");this.grid.appendChild(parseHTML(createTagRepeat("span",12,{"data-month":function dataMonth(e){return e}})))}_get(_getPrototypeOf(MonthsView.prototype),"init",this).call(this,e)}},{key:"setOptions",value:function setOptions(e){e.locale&&(this.monthNames=e.locale.monthsShort);if(hasProperty(e,"minDate"))if(e.minDate===void 0)this.minYear=this.minMonth=this.minDate=void 0;else{var t=new Date(e.minDate);this.minYear=t.getFullYear();this.minMonth=t.getMonth();this.minDate=t.setDate(1)}if(hasProperty(e,"maxDate"))if(e.maxDate===void 0)this.maxYear=this.maxMonth=this.maxDate=void 0;else{var r=new Date(e.maxDate);this.maxYear=r.getFullYear();this.maxMonth=r.getMonth();this.maxDate=dateValue(this.maxYear,this.maxMonth+1,0)}e.beforeShowMonth!==void 0&&(this.beforeShow=typeof e.beforeShowMonth==="function"?e.beforeShowMonth:void 0)}},{key:"updateFocus",value:function updateFocus(){var e=new Date(this.picker.viewDate);this.year=e.getFullYear();this.focused=e.getMonth()}},{key:"updateSelection",value:function updateSelection(){var e=this.picker.datepicker,t=e.dates,r=e.rangepicker;this.selected=t.reduce((function(e,t){var r=new Date(t);var a=r.getFullYear();var i=r.getMonth();e[a]===void 0?e[a]=[i]:pushUnique(e[a],i);return e}),{});r&&r.dates&&(this.range=r.dates.map((function(e){var t=new Date(e);return isNaN(t)?void 0:[t.getFullYear(),t.getMonth()]})))}},{key:"render",value:function render(){var e=this;this.disabled=[];this.picker.setViewSwitchLabel(this.year);this.picker.setPrevBtnDisabled(this.year<=this.minYear);this.picker.setNextBtnDisabled(this.year>=this.maxYear);var t=this.selected[this.year]||[];var r=this.year<this.minYear||this.year>this.maxYear;var a=this.year===this.minYear;var i=this.year===this.maxYear;var n=computeMonthRange(this.range,this.year);Array.from(this.grid.children).forEach((function(s,o){var l=s.classList;var c=dateValue(e.year,o,1);s.className="datepicker-cell hover:bg-gray-100 dark:hover:bg-gray-600 block flex-1 leading-9 border-0 rounded-lg cursor-pointer text-center text-gray-900 dark:text-white font-semibold text-sm ".concat(e.cellClass);e.isMinView&&(s.dataset.date=c);s.textContent=e.monthNames[o];(r||a&&o<e.minMonth||i&&o>e.maxMonth)&&l.add("disabled");if(n){var u=_slicedToArray(n,2),h=u[0],f=u[1];o>h&&o<f&&l.add("range");o===h&&l.add("range-start");o===f&&l.add("range-end")}if(t.includes(o)){l.add("selected","bg-blue-700","!bg-primary-700","text-white","dark:bg-blue-600","dark:!bg-primary-600","dark:text-white");l.remove("text-gray-900","hover:bg-gray-100","dark:text-white","dark:hover:bg-gray-600")}o===e.focused&&l.add("focused");e.beforeShow&&e.performBeforeHook(s,o,c)}))}},{key:"refresh",value:function refresh(){var e=this;var t=this.selected[this.year]||[];var r=computeMonthRange(this.range,this.year)||[],a=_slicedToArray(r,2),i=a[0],n=a[1];this.grid.querySelectorAll(".range, .range-start, .range-end, .selected, .focused").forEach((function(e){e.classList.remove("range","range-start","range-end","selected","bg-blue-700","!bg-primary-700","dark:bg-blue-600","dark:!bg-primary-700","dark:text-white","text-white","focused");e.classList.add("text-gray-900","hover:bg-gray-100","dark:text-white","dark:hover:bg-gray-600")}));Array.from(this.grid.children).forEach((function(r,a){var s=r.classList;a>i&&a<n&&s.add("range");a===i&&s.add("range-start");a===n&&s.add("range-end");if(t.includes(a)){s.add("selected","bg-blue-700","!bg-primary-700","text-white","dark:bg-blue-600","dark:!bg-primary-600","dark:text-white");s.remove("text-gray-900","hover:bg-gray-100","dark:text-white","dark:hover:bg-gray-600")}a===e.focused&&s.add("focused")}))}},{key:"refreshFocus",value:function refreshFocus(){this.grid.querySelectorAll(".focused").forEach((function(e){e.classList.remove("focused")}));this.grid.children[this.focused].classList.add("focused")}}])}(x);function toTitleCase(e){return _toConsumableArray(e).reduce((function(e,t,r){return e+(r?t:t.toUpperCase())}),"")}var _=function(e){function YearsView(e,t){_classCallCheck(this,YearsView);return _callSuper(this,YearsView,[e,t])}_inherits(YearsView,e);return _createClass(YearsView,[{key:"init",value:function init(e){var t=!(arguments.length>1&&arguments[1]!==void 0)||arguments[1];if(t){this.navStep=this.step*10;this.beforeShowOption="beforeShow".concat(toTitleCase(this.cellClass));this.grid=this.element;this.element.classList.add(this.name,"datepicker-grid","w-64","grid","grid-cols-4");this.grid.appendChild(parseHTML(createTagRepeat("span",12)))}_get(_getPrototypeOf(YearsView.prototype),"init",this).call(this,e)}},{key:"setOptions",value:function setOptions(e){if(hasProperty(e,"minDate"))if(e.minDate===void 0)this.minYear=this.minDate=void 0;else{this.minYear=startOfYearPeriod(e.minDate,this.step);this.minDate=dateValue(this.minYear,0,1)}if(hasProperty(e,"maxDate"))if(e.maxDate===void 0)this.maxYear=this.maxDate=void 0;else{this.maxYear=startOfYearPeriod(e.maxDate,this.step);this.maxDate=dateValue(this.maxYear,11,31)}if(e[this.beforeShowOption]!==void 0){var t=e[this.beforeShowOption];this.beforeShow=typeof t==="function"?t:void 0}}},{key:"updateFocus",value:function updateFocus(){var e=new Date(this.picker.viewDate);var t=startOfYearPeriod(e,this.navStep);var r=t+9*this.step;this.first=t;this.last=r;this.start=t-this.step;this.focused=startOfYearPeriod(e,this.step)}},{key:"updateSelection",value:function updateSelection(){var e=this;var t=this.picker.datepicker,r=t.dates,a=t.rangepicker;this.selected=r.reduce((function(t,r){return pushUnique(t,startOfYearPeriod(r,e.step))}),[]);a&&a.dates&&(this.range=a.dates.map((function(t){if(t!==void 0)return startOfYearPeriod(t,e.step)})))}},{key:"render",value:function render(){var e=this;this.disabled=[];this.picker.setViewSwitchLabel("".concat(this.first,"-").concat(this.last));this.picker.setPrevBtnDisabled(this.first<=this.minYear);this.picker.setNextBtnDisabled(this.last>=this.maxYear);Array.from(this.grid.children).forEach((function(t,r){var a=t.classList;var i=e.start+r*e.step;var n=dateValue(i,0,1);t.className="datepicker-cell hover:bg-gray-100 dark:hover:bg-gray-600 block flex-1 leading-9 border-0 rounded-lg cursor-pointer text-center text-gray-900 dark:text-white font-semibold text-sm ".concat(e.cellClass);e.isMinView&&(t.dataset.date=n);t.textContent=t.dataset.year=i;r===0?a.add("prev"):r===11&&a.add("next");(i<e.minYear||i>e.maxYear)&&a.add("disabled");if(e.range){var s=_slicedToArray(e.range,2),o=s[0],l=s[1];i>o&&i<l&&a.add("range");i===o&&a.add("range-start");i===l&&a.add("range-end")}if(e.selected.includes(i)){a.add("selected","bg-blue-700","!bg-primary-700","text-white","dark:bg-blue-600","dark:!bg-primary-600","dark:text-white");a.remove("text-gray-900","hover:bg-gray-100","dark:text-white","dark:hover:bg-gray-600")}i===e.focused&&a.add("focused");e.beforeShow&&e.performBeforeHook(t,i,n)}))}},{key:"refresh",value:function refresh(){var e=this;var t=this.range||[],r=_slicedToArray(t,2),a=r[0],i=r[1];this.grid.querySelectorAll(".range, .range-start, .range-end, .selected, .focused").forEach((function(e){e.classList.remove("range","range-start","range-end","selected","bg-blue-700","!bg-primary-700","text-white","dark:bg-blue-600","dark!bg-primary-600","dark:text-white","focused")}));Array.from(this.grid.children).forEach((function(t){var r=Number(t.textContent);var n=t.classList;r>a&&r<i&&n.add("range");r===a&&n.add("range-start");r===i&&n.add("range-end");if(e.selected.includes(r)){n.add("selected","bg-blue-700","!bg-primary-700","text-white","dark:bg-blue-600","dark:!bg-primary-600","dark:text-white");n.remove("text-gray-900","hover:bg-gray-100","dark:text-white","dark:hover:bg-gray-600")}r===e.focused&&n.add("focused")}))}},{key:"refreshFocus",value:function refreshFocus(){var e=Math.round((this.focused-this.start)/this.step);this.grid.querySelectorAll(".focused").forEach((function(e){e.classList.remove("focused")}));this.grid.children[e].classList.add("focused")}}])}(x);function triggerDatepickerEvent(e,t){var r={date:e.getDate(),viewDate:new Date(e.picker.viewDate),viewId:e.picker.currentView.id,datepicker:e};e.element.dispatchEvent(new CustomEvent(t,{detail:r}))}function goToPrevOrNext(e,t){var r=e.config,a=r.minDate,i=r.maxDate;var n=e.picker,s=n.currentView,o=n.viewDate;var l;switch(s.id){case 0:l=addMonths(o,t);break;case 1:l=addYears(o,t);break;default:l=addYears(o,t*s.navStep)}l=limitToRange(l,a,i);e.picker.changeFocus(l).render()}function switchView(e){var t=e.picker.currentView.id;t!==e.config.maxView&&e.picker.changeView(t+1).render()}function unfocus(e){if(e.config.updateOnBlur)e.update({autohide:true});else{e.refresh("input");e.hide()}}function goToSelectedMonthOrYear(e,t){var r=e.picker;var a=new Date(r.viewDate);var i=r.currentView.id;var n=i===1?addMonths(a,t-a.getMonth()):addYears(a,t-a.getFullYear());r.changeFocus(n).changeView(i-1).render()}function onClickTodayBtn(e){var t=e.picker;var r=today();if(e.config.todayBtnMode===1){if(e.config.autohide){e.setDate(r);return}e.setDate(r,{render:false});t.update()}t.viewDate!==r&&t.changeFocus(r);t.changeView(0).render()}function onClickClearBtn(e){e.setDate({clear:true})}function onClickViewSwitch(e){switchView(e)}function onClickPrevBtn(e){goToPrevOrNext(e,-1)}function onClickNextBtn(e){goToPrevOrNext(e,1)}function onClickView(e,t){var r=findElementInEventPath(t,".datepicker-cell");if(r&&!r.classList.contains("disabled")){var a=e.picker.currentView,i=a.id,n=a.isMinView;n?e.setDate(Number(r.dataset.date)):goToSelectedMonthOrYear(e,Number(i===1?r.dataset.month:r.dataset.year))}}function onClickPicker(e){e.inline||e.config.disableTouchKeyboard||e.inputField.focus()}function processPickerOptions(e,t){if(t.title!==void 0)if(t.title){e.controls.title.textContent=t.title;showElement(e.controls.title)}else{e.controls.title.textContent="";hideElement(e.controls.title)}if(t.prevArrow){var r=e.controls.prevBtn;emptyChildNodes(r);t.prevArrow.forEach((function(e){r.appendChild(e.cloneNode(true))}))}if(t.nextArrow){var a=e.controls.nextBtn;emptyChildNodes(a);t.nextArrow.forEach((function(e){a.appendChild(e.cloneNode(true))}))}if(t.locale){e.controls.todayBtn.textContent=t.locale.today;e.controls.clearBtn.textContent=t.locale.clear}t.todayBtn!==void 0&&(t.todayBtn?showElement(e.controls.todayBtn):hideElement(e.controls.todayBtn));if(hasProperty(t,"minDate")||hasProperty(t,"maxDate")){var i=e.datepicker.config,n=i.minDate,s=i.maxDate;e.controls.todayBtn.disabled=!isInRange(today(),n,s)}t.clearBtn!==void 0&&(t.clearBtn?showElement(e.controls.clearBtn):hideElement(e.controls.clearBtn))}function computeResetViewDate(e){var t=e.dates,r=e.config;var a=t.length>0?lastItemOf(t):r.defaultViewDate;return limitToRange(a,r.minDate,r.maxDate)}function setViewDate(e,t){var r=new Date(e.viewDate);var a=new Date(t);var i=e.currentView,n=i.id,s=i.year,o=i.first,l=i.last;var c=a.getFullYear();e.viewDate=t;c!==r.getFullYear()&&triggerDatepickerEvent(e.datepicker,"changeYear");a.getMonth()!==r.getMonth()&&triggerDatepickerEvent(e.datepicker,"changeMonth");switch(n){case 0:return t<o||t>l;case 1:return c!==s;default:return c<o||c>l}}function getTextDirection(e){return window.getComputedStyle(e).direction}var S=function(){function Picker(e){_classCallCheck(this,Picker);this.datepicker=e;var t=k.replace(/%buttonClass%/g,e.config.buttonClass);var r=this.element=parseHTML(t).firstChild;var a=_slicedToArray(r.firstChild.children,3),i=a[0],n=a[1],s=a[2];var o=i.firstElementChild;var l=_slicedToArray(i.lastElementChild.children,3),c=l[0],u=l[1],h=l[2];var f=_slicedToArray(s.firstChild.children,2),g=f[0],v=f[1];var p={title:o,prevBtn:c,viewSwitch:u,nextBtn:h,todayBtn:g,clearBtn:v};this.main=n;this.controls=p;var b=e.inline?"inline":"dropdown";r.classList.add("datepicker-".concat(b));b==="dropdown"?r.classList.add("dropdown","absolute","top-0","left-0","z-50","pt-2"):null;processPickerOptions(this,e.config);this.viewDate=computeResetViewDate(e);registerListeners(e,[[r,"click",onClickPicker.bind(null,e),{capture:true}],[n,"click",onClickView.bind(null,e)],[p.viewSwitch,"click",onClickViewSwitch.bind(null,e)],[p.prevBtn,"click",onClickPrevBtn.bind(null,e)],[p.nextBtn,"click",onClickNextBtn.bind(null,e)],[p.todayBtn,"click",onClickTodayBtn.bind(null,e)],[p.clearBtn,"click",onClickClearBtn.bind(null,e)]]);this.views=[new O(this),new C(this),new _(this,{id:2,name:"years",cellClass:"year",step:1}),new _(this,{id:3,name:"decades",cellClass:"decade",step:10})];this.currentView=this.views[e.config.startView];this.currentView.render();this.main.appendChild(this.currentView.element);e.config.container.appendChild(this.element)}return _createClass(Picker,[{key:"setOptions",value:function setOptions(e){processPickerOptions(this,e);this.views.forEach((function(t){t.init(e,false)}));this.currentView.render()}},{key:"detach",value:function detach(){this.datepicker.config.container.removeChild(this.element)}},{key:"show",value:function show(){if(!this.active){this.element.classList.add("active","block");this.element.classList.remove("hidden");this.active=true;var e=this.datepicker;if(!e.inline){var t=getTextDirection(e.inputField);t!==getTextDirection(e.config.container)?this.element.dir=t:this.element.dir&&this.element.removeAttribute("dir");this.place();e.config.disableTouchKeyboard&&e.inputField.blur()}triggerDatepickerEvent(e,"show")}}},{key:"hide",value:function hide(){if(this.active){this.datepicker.exitEditMode();this.element.classList.remove("active","block");this.element.classList.add("active","block","hidden");this.active=false;triggerDatepickerEvent(this.datepicker,"hide")}}},{key:"place",value:function place(){var e=this.element,t=e.classList,r=e.style;var a=this.datepicker,i=a.config,n=a.inputField;var s=i.container;var o=this.element.getBoundingClientRect(),l=o.width,c=o.height;var u=s.getBoundingClientRect(),h=u.left,f=u.top,g=u.width;var v=n.getBoundingClientRect(),p=v.left,k=v.top,b=v.width,w=v.height;var x=i.orientation,O=x.x,C=x.y;var _;var S;var T;if(s===document.body){_=window.scrollY;S=p+window.scrollX;T=k+_}else{_=s.scrollTop;S=p-h;T=k-f+_}if(O==="auto")if(S<0){O="left";S=10}else O=S+l>g||getTextDirection(n)==="rtl"?"right":"left";O==="right"&&(S-=l-b);C==="auto"&&(C=T-c<_?"bottom":"top");C==="top"?T-=c:T+=w;t.remove("datepicker-orient-top","datepicker-orient-bottom","datepicker-orient-right","datepicker-orient-left");t.add("datepicker-orient-".concat(C),"datepicker-orient-".concat(O));r.top=T?"".concat(T,"px"):T;r.left=S?"".concat(S,"px"):S}},{key:"setViewSwitchLabel",value:function setViewSwitchLabel(e){this.controls.viewSwitch.textContent=e}},{key:"setPrevBtnDisabled",value:function setPrevBtnDisabled(e){this.controls.prevBtn.disabled=e}},{key:"setNextBtnDisabled",value:function setNextBtnDisabled(e){this.controls.nextBtn.disabled=e}},{key:"changeView",value:function changeView(e){var t=this.currentView;var r=this.views[e];if(r.id!==t.id){this.currentView=r;this._renderMethod="render";triggerDatepickerEvent(this.datepicker,"changeView");this.main.replaceChild(r.element,t.element)}return this}},{key:"changeFocus",value:function changeFocus(e){this._renderMethod=setViewDate(this,e)?"render":"refreshFocus";this.views.forEach((function(e){e.updateFocus()}));return this}},{key:"update",value:function update(){var e=computeResetViewDate(this.datepicker);this._renderMethod=setViewDate(this,e)?"render":"refresh";this.views.forEach((function(e){e.updateFocus();e.updateSelection()}));return this}},{key:"render",value:function render(){var e=!(arguments.length>0&&arguments[0]!==void 0)||arguments[0];var t=e&&this._renderMethod||"render";delete this._renderMethod;this.currentView[t]()}}])}();function findNextAvailableOne(e,t,r,a,i,n){if(isInRange(e,i,n)){if(a(e)){var s=t(e,r);return findNextAvailableOne(s,t,r,a,i,n)}return e}}function moveByArrowKey(e,t,r,a){var i=e.picker;var n=i.currentView;var s=n.step||1;var o=i.viewDate;var l;var c;switch(n.id){case 0:o=a?addDays(o,r*7):t.ctrlKey||t.metaKey?addYears(o,r):addDays(o,r);l=addDays;c=function testFn(e){return n.disabled.includes(e)};break;case 1:o=addMonths(o,a?r*4:r);l=addMonths;c=function testFn(e){var t=new Date(e);var r=n.year,a=n.disabled;return t.getFullYear()===r&&a.includes(t.getMonth())};break;default:o=addYears(o,r*(a?4:1)*s);l=addYears;c=function testFn(e){return n.disabled.includes(startOfYearPeriod(e,s))}}o=findNextAvailableOne(o,l,r<0?-s:s,c,n.minDate,n.maxDate);o!==void 0&&i.changeFocus(o).render()}function onKeydown(e,t){if(t.key!=="Tab"){var r=e.picker;var a=r.currentView,i=a.id,n=a.isMinView;if(r.active)if(e.editMode)switch(t.key){case"Escape":r.hide();break;case"Enter":e.exitEditMode({update:true,autohide:e.config.autohide});break;default:return}else switch(t.key){case"Escape":r.hide();break;case"ArrowLeft":if(t.ctrlKey||t.metaKey)goToPrevOrNext(e,-1);else{if(t.shiftKey){e.enterEditMode();return}moveByArrowKey(e,t,-1,false)}break;case"ArrowRight":if(t.ctrlKey||t.metaKey)goToPrevOrNext(e,1);else{if(t.shiftKey){e.enterEditMode();return}moveByArrowKey(e,t,1,false)}break;case"ArrowUp":if(t.ctrlKey||t.metaKey)switchView(e);else{if(t.shiftKey){e.enterEditMode();return}moveByArrowKey(e,t,-1,true)}break;case"ArrowDown":if(t.shiftKey&&!t.ctrlKey&&!t.metaKey){e.enterEditMode();return}moveByArrowKey(e,t,1,true);break;case"Enter":n?e.setDate(r.viewDate):r.changeView(i-1).render();break;case"Backspace":case"Delete":e.enterEditMode();return;default:t.key.length!==1||t.ctrlKey||t.metaKey||e.enterEditMode();return}else switch(t.key){case"ArrowDown":case"Escape":r.show();break;case"Enter":e.update();break;default:return}t.preventDefault();t.stopPropagation()}else unfocus(e)}function onFocus(e){e.config.showOnFocus&&!e._showing&&e.show()}function onMousedown(e,t){var r=t.target;if(e.picker.active||e.config.showOnClick){r._active=r===document.activeElement;r._clicking=setTimeout((function(){delete r._active;delete r._clicking}),2e3)}}function onClickInput(e,t){var r=t.target;if(r._clicking){clearTimeout(r._clicking);delete r._clicking;r._active&&e.enterEditMode();delete r._active;e.config.showOnClick&&e.show()}}function onPaste(e,t){t.clipboardData.types.includes("text/plain")&&e.enterEditMode()}function onClickOutside(e,t){var r=e.element;if(r===document.activeElement){var a=e.picker.element;findElementInEventPath(t,(function(e){return e===r||e===a}))||unfocus(e)}}function stringifyDates(e,t){return e.map((function(e){return formatDate(e,t.format,t.locale)})).join(t.dateDelimiter)}function processInputDates(e,t){var r=arguments.length>2&&arguments[2]!==void 0&&arguments[2];var a=e.config,i=e.dates,n=e.rangepicker;if(t.length===0)return r?[]:void 0;var s=n&&e===n.datepickers[1];var o=t.reduce((function(e,t){var r=parseDate(t,a.format,a.locale);if(r===void 0)return e;if(a.pickLevel>0){var i=new Date(r);r=a.pickLevel===1?s?i.setMonth(i.getMonth()+1,0):i.setDate(1):s?i.setFullYear(i.getFullYear()+1,0,0):i.setMonth(0,1)}!isInRange(r,a.minDate,a.maxDate)||e.includes(r)||a.datesDisabled.includes(r)||a.daysOfWeekDisabled.includes(new Date(r).getDay())||e.push(r);return e}),[]);if(o.length!==0){a.multidate&&!r&&(o=o.reduce((function(e,t){i.includes(t)||e.push(t);return e}),i.filter((function(e){return!o.includes(e)}))));return a.maxNumberOfDates&&o.length>a.maxNumberOfDates?o.slice(a.maxNumberOfDates*-1):o}}function refreshUI(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:3;var r=!(arguments.length>2&&arguments[2]!==void 0)||arguments[2];var a=e.config,i=e.picker,n=e.inputField;if(t&2){var s=i.active?a.pickLevel:a.startView;i.update().changeView(s).render(r)}t&1&&n&&(n.value=stringifyDates(e.dates,a))}function _setDate(e,t,r){var a=r.clear,i=r.render,n=r.autohide;i===void 0&&(i=true);i?n===void 0&&(n=e.config.autohide):n=false;var s=processInputDates(e,t,a);if(s){if(s.toString()!==e.dates.toString()){e.dates=s;refreshUI(e,i?3:1);triggerDatepickerEvent(e,"changeDate")}else refreshUI(e,1);n&&e.hide()}}var T=function(){
/**
   * Create a date picker
   * @param  {Element} element - element to bind a date picker
   * @param  {Object} [options] - config options
   * @param  {DateRangePicker} [rangepicker] - DateRangePicker instance the
   * date picker belongs to. Use this only when creating date picker as a part
   * of date range picker
   */
function Datepicker(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};var r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:void 0;_classCallCheck(this,Datepicker);e.datepicker=this;this.element=e;var a=this.config=Object.assign({buttonClass:t.buttonClass&&String(t.buttonClass)||"button",container:document.body,defaultViewDate:today(),maxDate:void 0,minDate:void 0},processOptions(h,this));this._options=t;Object.assign(a,processOptions(t,this));var i=this.inline=e.tagName!=="INPUT";var n;var s;if(i){a.container=e;s=stringToArray(e.dataset.date,a.dateDelimiter);delete e.dataset.date}else{var o=t.container?document.querySelector(t.container):null;o&&(a.container=o);n=this.inputField=e;n.classList.add("datepicker-input");s=stringToArray(n.value,a.dateDelimiter)}if(r){var l=r.inputs.indexOf(n);var c=r.datepickers;if(l<0||l>1||!Array.isArray(c))throw Error("Invalid rangepicker object.");c[l]=this;Object.defineProperty(this,"rangepicker",{get:function get(){return r}})}this.dates=[];var u=processInputDates(this,s);u&&u.length>0&&(this.dates=u);n&&(n.value=stringifyDates(this.dates,a));var f=this.picker=new S(this);if(i)this.show();else{var g=onClickOutside.bind(null,this);var v=[[n,"keydown",onKeydown.bind(null,this)],[n,"focus",onFocus.bind(null,this)],[n,"mousedown",onMousedown.bind(null,this)],[n,"click",onClickInput.bind(null,this)],[n,"paste",onPaste.bind(null,this)],[document,"mousedown",g],[document,"touchstart",g],[window,"resize",f.place.bind(f)]];registerListeners(this,v)}}
/**
   * Format Date object or time value in given format and language
   * @param  {Date|Number} date - date or time value to format
   * @param  {String|Object} format - format string or object that contains
   * toDisplay() custom formatter, whose signature is
   * - args:
   *   - date: {Date} - Date instance of the date passed to the method
   *   - format: {Object} - the format object passed to the method
   *   - locale: {Object} - locale for the language specified by `lang`
   * - return:
   *     {String} formatted date
   * @param  {String} [lang=en] - language code for the locale to use
   * @return {String} formatted date
   */return _createClass(Datepicker,[{key:"active",get:
/**
     * @type {Boolean} - Whether the picker element is shown. `true` whne shown
     */
function get(){return!!(this.picker&&this.picker.active)}
/**
     * @type {HTMLDivElement} - DOM object of picker element
     */},{key:"pickerElement",get:function get(){return this.picker?this.picker.element:void 0}
/**
     * Set new values to the config options
     * @param {Object} options - config options to update
     */},{key:"setOptions",value:function setOptions(e){var t=this.picker;var r=processOptions(e,this);Object.assign(this._options,e);Object.assign(this.config,r);t.setOptions(r);refreshUI(this,3)}},{key:"show",value:function show(){if(this.inputField){if(this.inputField.disabled)return;if(this.inputField!==document.activeElement){this._showing=true;this.inputField.focus();delete this._showing}}this.picker.show()}},{key:"hide",value:function hide(){if(!this.inline){this.picker.hide();this.picker.update().changeView(this.config.startView).render()}}},{key:"destroy",value:function destroy(){this.hide();unregisterListeners(this);this.picker.detach();this.inline||this.inputField.classList.remove("datepicker-input");delete this.element.datepicker;return this}
/**
     * Get the selected date(s)
     *
     * The method returns a Date object of selected date by default, and returns
     * an array of selected dates in multidate mode. If format string is passed,
     * it returns date string(s) formatted in given format.
     *
     * @param  {String} [format] - Format string to stringify the date(s)
     * @return {Date|String|Date[]|String[]} - selected date(s), or if none is
     * selected, empty array in multidate mode and untitled in sigledate mode
     */},{key:"getDate",value:function getDate(){var e=this;var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0;var r=t?function(r){return formatDate(r,t,e.config.locale)}:function(e){return new Date(e)};return this.config.multidate?this.dates.map(r):this.dates.length>0?r(this.dates[0]):void 0}
/**
     * Set selected date(s)
     *
     * In multidate mode, you can pass multiple dates as a series of arguments
     * or an array. (Since each date is parsed individually, the type of the
     * dates doesn't have to be the same.)
     * The given dates are used to toggle the select status of each date. The
     * number of selected dates is kept from exceeding the length set to
     * maxNumberOfDates.
     *
     * With clear: true option, the method can be used to clear the selection
     * and to replace the selection instead of toggling in multidate mode.
     * If the option is passed with no date arguments or an empty dates array,
     * it works as "clear" (clear the selection then set nothing), and if the
     * option is passed with new dates to select, it works as "replace" (clear
     * the selection then set the given dates)
     *
     * When render: false option is used, the method omits re-rendering the
     * picker element. In this case, you need to call refresh() method later in
     * order for the picker element to reflect the changes. The input field is
     * refreshed always regardless of this option.
     *
     * When invalid (unparsable, repeated, disabled or out-of-range) dates are
     * passed, the method ignores them and applies only valid ones. In the case
     * that all the given dates are invalid, which is distinguished from passing
     * no dates, the method considers it as an error and leaves the selection
     * untouched.
     *
     * @param {...(Date|Number|String)|Array} [dates] - Date strings, Date
     * objects, time values or mix of those for new selection
     * @param {Object} [options] - function options
     * - clear: {boolean} - Whether to clear the existing selection
     *     defualt: false
     * - render: {boolean} - Whether to re-render the picker element
     *     default: true
     * - autohide: {boolean} - Whether to hide the picker element after re-render
     *     Ignored when used with render: false
     *     default: config.autohide
     */},{key:"setDate",value:function setDate(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var a=[].concat(t);var i={};var n=lastItemOf(t);_typeof(n)!=="object"||Array.isArray(n)||n instanceof Date||!n||Object.assign(i,a.pop());var s=Array.isArray(a[0])?a[0]:a;_setDate(this,s,i)}
/**
     * Update the selected date(s) with input field's value
     * Not available on inline picker
     *
     * The input field will be refreshed with properly formatted date string.
     *
     * @param  {Object} [options] - function options
     * - autohide: {boolean} - whether to hide the picker element after refresh
     *     default: false
     */},{key:"update",value:function update(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0;if(!this.inline){var t={clear:true,autohide:!!(e&&e.autohide)};var r=stringToArray(this.inputField.value,this.config.dateDelimiter);_setDate(this,r,t)}}
/**
     * Refresh the picker element and the associated input field
     * @param {String} [target] - target item when refreshing one item only
     * 'picker' or 'input'
     * @param {Boolean} [forceRender] - whether to re-render the picker element
     * regardless of its state instead of optimized refresh
     */},{key:"refresh",value:function refresh(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0;var t=arguments.length>1&&arguments[1]!==void 0&&arguments[1];if(e&&typeof e!=="string"){t=e;e=void 0}var r;r=e==="picker"?2:e==="input"?1:3;refreshUI(this,r,!t)}},{key:"enterEditMode",value:function enterEditMode(){if(!this.inline&&this.picker.active&&!this.editMode){this.editMode=true;this.inputField.classList.add("in-edit","border-blue-700","!border-primary-700")}}
/**
     * Exit from edit mode
     * Not available on inline picker
     * @param  {Object} [options] - function options
     * - update: {boolean} - whether to call update() after exiting
     *     If false, input field is revert to the existing selection
     *     default: false
     */},{key:"exitEditMode",value:function exitEditMode(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0;if(!this.inline&&this.editMode){var t=Object.assign({update:false},e);delete this.editMode;this.inputField.classList.remove("in-edit","border-blue-700","!border-primary-700");t.update&&this.update(t)}}}],[{key:"formatDate",value:function formatDate$1(e,t,r){return formatDate(e,t,r&&u[r]||u.en)}
/**
     * Parse date string
     * @param  {String|Date|Number} dateStr - date string, Date object or time
     * value to parse
     * @param  {String|Object} format - format string or object that contains
     * toValue() custom parser, whose signature is
     * - args:
     *   - dateStr: {String|Date|Number} - the dateStr passed to the method
     *   - format: {Object} - the format object passed to the method
     *   - locale: {Object} - locale for the language specified by `lang`
     * - return:
     *     {Date|Number} parsed date or its time value
     * @param  {String} [lang=en] - language code for the locale to use
     * @return {Number} time value of parsed date
     */},{key:"parseDate",value:function parseDate$1(e,t,r){return parseDate(e,t,r&&u[r]||u.en)}
/**
     * @type {Object} - Installed locales in `[languageCode]: localeObject` format
     * en`:_English (US)_ is pre-installed.
     */},{key:"locales",get:function get(){return u}}])}();function filterOptions(e){var t=Object.assign({},e);delete t.inputs;delete t.allowOneSidedRange;delete t.maxNumberOfDates;return t}function setupDatepicker(e,t,r,a){registerListeners(e,[[r,"changeDate",t]]);new T(r,a,e)}function onChangeDate(e,t){if(!e._updating){e._updating=true;var r=t.target;if(r.datepicker!==void 0){var a=e.datepickers;var i={render:false};var n=e.inputs.indexOf(r);var s=n===0?1:0;var o=a[n].dates[0];var l=a[s].dates[0];if(o!==void 0&&l!==void 0){if(n===0&&o>l){a[0].setDate(l,i);a[1].setDate(o,i)}else if(n===1&&o<l){a[0].setDate(o,i);a[1].setDate(l,i)}}else if(!e.allowOneSidedRange&&(o!==void 0||l!==void 0)){i.clear=true;a[s].setDate(a[n].dates,i)}a[0].picker.update().render();a[1].picker.update().render();delete e._updating}}}var V=function(){
/**
   * Create a date range picker
   * @param  {Element} element - element to bind a date range picker
   * @param  {Object} [options] - config options
   */
function DateRangePicker(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};_classCallCheck(this,DateRangePicker);var r=Array.isArray(t.inputs)?t.inputs:Array.from(e.querySelectorAll("input"));if(!(r.length<2)){e.rangepicker=this;this.element=e;this.inputs=r.slice(0,2);this.allowOneSidedRange=!!t.allowOneSidedRange;var a=onChangeDate.bind(null,this);var i=filterOptions(t);var n=[];Object.defineProperty(this,"datepickers",{get:function get(){return n}});setupDatepicker(this,a,this.inputs[0],i);setupDatepicker(this,a,this.inputs[1],i);Object.freeze(n);n[0].dates.length>0?onChangeDate(this,{target:this.inputs[0]}):n[1].dates.length>0&&onChangeDate(this,{target:this.inputs[1]})}}
/**
   * @type {Array} - selected date of the linked date pickers
   */return _createClass(DateRangePicker,[{key:"dates",get:function get(){return this.datepickers.length===2?[this.datepickers[0].dates[0],this.datepickers[1].dates[0]]:void 0}
/**
     * Set new values to the config options
     * @param {Object} options - config options to update
     */},{key:"setOptions",value:function setOptions(e){this.allowOneSidedRange=!!e.allowOneSidedRange;var t=filterOptions(e);this.datepickers[0].setOptions(t);this.datepickers[1].setOptions(t)}},{key:"destroy",value:function destroy(){this.datepickers[0].destroy();this.datepickers[1].destroy();unregisterListeners(this);delete this.element.rangepicker}
/**
     * Get the start and end dates of the date range
     *
     * The method returns Date objects by default. If format string is passed,
     * it returns date strings formatted in given format.
     * The result array always contains 2 items (start date/end date) and
     * undefined is used for unselected side. (e.g. If none is selected,
     * the result will be [undefined, undefined]. If only the end date is set
     * when allowOneSidedRange config option is true, [undefined, endDate] will
     * be returned.)
     *
     * @param  {String} [format] - Format string to stringify the dates
     * @return {Array} - Start and end dates
     */},{key:"getDates",value:function getDates(){var e=this;var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:void 0;var r=t?function(r){return formatDate(r,t,e.datepickers[0].config.locale)}:function(e){return new Date(e)};return this.dates.map((function(e){return e===void 0?e:r(e)}))}
/**
     * Set the start and end dates of the date range
     *
     * The method calls datepicker.setDate() internally using each of the
     * arguments in start→end order.
     *
     * When a clear: true option object is passed instead of a date, the method
     * clears the date.
     *
     * If an invalid date, the same date as the current one or an option object
     * without clear: true is passed, the method considers that argument as an
     * "ineffective" argument because calling datepicker.setDate() with those
     * values makes no changes to the date selection.
     *
     * When the allowOneSidedRange config option is false, passing {clear: true}
     * to clear the range works only when it is done to the last effective
     * argument (in other words, passed to rangeEnd or to rangeStart along with
     * ineffective rangeEnd). This is because when the date range is changed,
     * it gets normalized based on the last change at the end of the changing
     * process.
     *
     * @param {Date|Number|String|Object} rangeStart - Start date of the range
     * or {clear: true} to clear the date
     * @param {Date|Number|String|Object} rangeEnd - End date of the range
     * or {clear: true} to clear the date
     */},{key:"setDates",value:function setDates(e,t){var r=_slicedToArray(this.datepickers,2),a=r[0],i=r[1];var n=this.dates;this._updating=true;a.setDate(e);i.setDate(t);delete this._updating;i.dates[0]!==n[1]?onChangeDate(this,{target:this.inputs[1]}):a.dates[0]!==n[0]&&onChangeDate(this,{target:this.inputs[0]})}}])}();export{V as DateRangePicker,T as Datepicker};

