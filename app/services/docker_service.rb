class DockerService
  class << self
    def init_base_services
      begin
        # Get the path to the docker-compose.yml file
        compose_file_path = Rails.root.join('syncbot-docker-compose.yaml')

        unless File.exist?(compose_file_path)
          return { success: false, message: "Docker Compose file not found at #{compose_file_path}" }
        end

        # Run docker compose up -d command with the specific file
        result = system("cd #{Rails.root} && docker compose -f syncbot-docker-compose.yaml up -d")

        if result
          return { success: true, message: "Base services initialized successfully" }
        else
          return { success: false, message: "Failed to initialize base services. Check Docker logs for details." }
        end
      rescue => e
        return { success: false, message: "Error initializing base services: #{e.message}" }
      end
    end

    def destroy_all_containers
      begin
        # Get all containers
        containers = Docker::Container.all(all: true)

        if containers.empty?
          return { success: true, message: "No containers found to destroy" }
        end

        # Track success and failures
        success_count = 0
        failed_containers = []

        # Stop and remove each container
        containers.each do |container|
          container_name = container.info['Names'].first.sub(/^\//, '')

          begin
            # Stop the container if it's running
            if container.info['State'] == 'running'
              container.stop
            end

            # Remove the container
            container.delete(force: true)

            success_count += 1
          rescue => e
            failed_containers << "#{container_name} (#{e.message})"
          end
        end

        if failed_containers.empty?
          system('docker system prune -f')
          return { success: true, message: "Successfully destroyed all #{success_count} containers" }
        else
          return {
            success: false,
            message: "Destroyed #{success_count} containers, but failed to destroy #{failed_containers.size} containers: #{failed_containers.join(', ')}"
          }
        end
      rescue => e
        return { success: false, message: "Error destroying containers: #{e.message}" }
      end
    end
    def clone_celery_worker(name_suffix, host_port = nil)
      begin
        # Validate name_suffix
        if name_suffix.blank?
          return { success: false, message: "Name suffix cannot be blank" }
        end

        # Define container names
        new_mt5_name = "mt5_#{name_suffix}"
        new_celery_name = "celery_worker_#{name_suffix}"

        # Use the custom network name defined in syncbot-docker-compose.yaml
        network_name = "kamal"

        # Create MT5 container with docker run command
        port_binding = host_port.present? ? "-p #{host_port}:3000" : ""
        mt5_cmd = "docker run -d \
          --name #{new_mt5_name} \
          --network #{network_name} \
          #{port_binding} \
          -e TZ=Asia/Saigon \
          -e CUSTOM_USER=${MT5_USER} \
          -e PASSWORD=${MT5_PASSWORD} \
          --restart unless-stopped \
          flamintdao/mt5:latest"

        # Execute the MT5 container creation command
        mt5_result = system(mt5_cmd)
        return { success: false, message: "Failed to create MT5 container. Command: #{mt5_cmd}" } unless mt5_result

        # Create celery_worker container with docker run command
        celery_cmd = "docker run -d \
          --name #{new_celery_name} \
          --network #{network_name} \
          -v ${VOLUME_SCRIPT_PATH}:/scripts \
          -e TZ=Asia/Saigon \
          -e CONNECTION_STRING=${INTERNAL_CONNECTION_STRING} \
          -e CELERY_BROKER_URL=${CELERY_BROKER_URL} \
          -e PYTHONUNBUFFERED=1 \
          -e LOG_LEVEL=DEBUG \
          -e MT5_URL=#{new_mt5_name} \
          --restart unless-stopped \
          flamintdao/synctradepython \
          celery -A tasks worker --loglevel=debug --concurrency=1"

        # Execute the celery_worker container creation command
        celery_result = system(celery_cmd)
        unless celery_result
          # Clean up MT5 container if celery worker creation fails
          system("docker rm -f #{new_mt5_name}")
          return { success: false, message: "Failed to create celery_worker container. Command: #{celery_cmd}" }
        end

        { success: true, message: "Successfully created #{new_mt5_name} and #{new_celery_name} containers" }
      rescue => e
        # Clean up containers if they were created
        system("docker rm -f #{new_mt5_name} 2>/dev/null")
        system("docker rm -f #{new_celery_name} 2>/dev/null")

        return { success: false, message: "Error cloning containers: #{e.message}" }
      end
    end

    def add_mt5_server(server_name, container_name = nil)
      begin
        # Get all MT5 containers
        mt5_containers = Docker::Container.all(all: true).select do |c|
          name = c.info['Names'].first.sub(/^\//, '')
          name.include?('mt5') && c.info['State'] == 'running'
        end

        if mt5_containers.empty?
          return { success: false, message: "No running MT5 containers found" }
        end

        # If container_name is provided and not 'all', filter to just that container
        if container_name.present? && container_name != 'all'
          mt5_containers = mt5_containers.select do |c|
            name = c.info['Names'].first.sub(/^\//, '')
            name == container_name
          end

          if mt5_containers.empty?
            return { success: false, message: "MT5 container '#{container_name}' not found or not running" }
          end
        end

        # Execute the command for each container
        results = []
        mt5_containers.each do |container|
          container_name = container.info['Names'].first.sub(/^\//, '')
          command = "docker exec -u 911:911 #{container_name} wine cmd /c \"python /scripts/mt5serversearch.py #{server_name}\""

          # Execute the command
          output = `#{command} 2>&1`
          exit_status = $?.exitstatus

          if exit_status == 0
            results << { container: container_name, success: true }
          else
            results << { container: container_name, success: false, error: output }
          end
        end

        # Process results
        success_count = results.count { |r| r[:success] }
        failure_count = results.count { |r| !r[:success] }

        if failure_count == 0
          return {
            success: true,
            message: "Successfully added server '#{server_name}' to #{success_count} MT5 container(s)"
          }
        elsif success_count > 0
          failed_containers = results.select { |r| !r[:success] }.map { |r| r[:container] }
          return {
            success: true,
            message: "Added server '#{server_name}' to #{success_count} MT5 container(s), but failed for: #{failed_containers.join(', ')}"
          }
        else
          errors = results.map { |r| "#{r[:container]}: #{r[:error]}" }.join("\n")
          return {
            success: false,
            message: "Failed to add server '#{server_name}' to any MT5 containers. Errors: #{errors}"
          }
        end
      rescue => e
        return { success: false, message: "Error adding server to MT5 containers: #{e.message}" }
      end
    end
  end
end
