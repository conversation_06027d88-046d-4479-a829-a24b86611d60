class ScriptManagerService
  include HTTParty
  base_uri ENV['PYTHON_SCRIPT_MANAGER_URL']

  def self.start_script(script_id)
    # Check MT5 servers for copier scripts before starting
    script = Script.find_by(id: script_id)
    if script&.type == 'Copier'
      check_mt5_server(script)
    end

    # Send as query parameter for FastAPI
    post('/scripts/start', query: { script_id: script_id })
  end

  def self.stop_script(script_id)
    # Send as query parameter for FastAPI
    post('/scripts/stop', query: { script_id: script_id })
  end

  def self.script_status(script_id)
    get('/scripts/status', query: { script_id: script_id })
  end

  def self.script_logs(script_id, lines = 100)
    # Get the logs for a script
    get('/scripts/logs', query: { script_id: script_id, lines: lines })
  end

  def self.system_status
    # Get the system status
    get('/system/status')
  end

  # Check MT5 servers for copier scripts before starting
  def self.check_mt5_server(copier_script)
    return unless copier_script.is_a?(Copier)

    # Get MT5 URL from script config
    config = copier_script.get_config
    mt5_url = config[:MT5_URL] || ENV['MT5_URL_DEDAULT']

    return if mt5_url.blank?

    # Find or create KnownMt5Server record
    known_server = KnownMt5Server.find_or_create_by(mt5_url: mt5_url)

    # Get all MT5 accounts attached to this copier script
    mt5_accounts = copier_script.to_accounts.includes(:accountable)
                                .where(accountable_type: 'Mt5Account')
                                .map(&:accountable)

    # Collect unique server names from MT5 accounts
    script_server_names = mt5_accounts.map(&:server).compact.uniq

    # Find server names that are not in the known servers list
    unknown_servers = script_server_names - known_server.server_name

    # Execute docker command for unknown servers
    begin
      Rails.logger.info("Searching for unknown MT5 server: #{unknown_servers.join(', ')} in container: #{mt5_url}")

      command = "docker exec -u 911:911 #{mt5_url} wine cmd /c \"python /scripts/mt5serversearch.py #{unknown_servers.join(' ')}\""

      # Execute the command
      output = `#{command} 2>&1`
      exit_status = $?.exitstatus

      if exit_status == 0
        Rails.logger.info("Successfully searched for MT5 server #{unknown_servers.join(', ')}: #{output.strip}")

        # Add the server name to the known servers list
        unknown_servers.each do |server_name|
          known_server.add_server_name(server_name)
        end
      else
        Rails.logger.error("Failed to search for MT5 server #{unknown_servers.join(', ')}: #{output.strip}")
      end

    rescue => e
      Rails.logger.error("Error executing MT5 server search for #{unknown_servers.join(', ')}: #{e.message}")
    end
  end
end
