class BotActionJob < ApplicationJob
  queue_as :default

  def perform(action, bot_id, user_id)
    @bot = Script.find_by(id: bot_id)
    @user = User.find_by(id: user_id)

    return unless @bot && @user

    # Ensure the bot belongs to the user
    return unless @bot.user_id == @user.id

    case action
    when 'start'
      start_bot
    when 'stop'
      stop_bot
    when 'restart'
      restart_bot
    else
      Rails.logger.error("Unknown bot action: #{action}")
    end
  end

  private

  def start_bot
    response = ScriptManagerService.start_script(@bot.id)

    if response && response.success?
      # Broadcast success message
      broadcast_result('notice', "Bot #{@bot.type.downcase} was successfully started.")
    else
      # Broadcast error message
      broadcast_result('alert', "Failed to start bot #{@bot.type.downcase}.")
    end
  end

  def stop_bot
    response = ScriptManagerService.stop_script(@bot.id)

    if response && response.success?
      # Broadcast success message
      broadcast_result('notice', "Bot #{@bot.type.downcase} was successfully stopped.")
    else
      # Broadcast error message
      broadcast_result('alert', "Failed to stop bot #{@bot.type.downcase}.")
    end
  end

  def restart_bot
    # First stop the bot
    stop_response = ScriptManagerService.stop_script(@bot.id)

    if stop_response && stop_response.success?
      Rails.logger.info("Bot #{@bot.id} stopped successfully, now starting...")

      # Wait a moment before starting to ensure clean shutdown
      sleep(2)

      # Then start the bot
      start_response = ScriptManagerService.start_script(@bot.id)

      if start_response && start_response.success?
        # Broadcast success message
        broadcast_result('notice', "Bot #{@bot.type.downcase} was successfully restarted.")
      else
        # Broadcast error message
        broadcast_result('alert', "Bot #{@bot.type.downcase} was stopped but failed to start again.")
      end
    else
      # If stop failed, don't attempt to start
      broadcast_result('alert', "Failed to restart bot #{@bot.type.downcase} - could not stop the bot.")
    end
  end
end
