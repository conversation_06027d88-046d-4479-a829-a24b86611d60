class DockerContainerJob < ApplicationJob
  queue_as :default

  def perform(action, user_id, container_id = nil)
    @user = User.find_by(id: user_id)

    return unless @user && @user.admin?

    case action
    when 'init_base_services'
      init_base_services
    when 'start_container'
      start_container(container_id)
    when 'stop_container'
      stop_container(container_id)
    when 'restart_container'
      restart_container(container_id)
    when 'destroy_container'
      destroy_container(container_id)
    else
      Rails.logger.error("Unknown docker container action: #{action}")
    end
  end

  private

  def init_base_services
    result = DockerService.init_base_services

    if result[:success]
      broadcast_result('notice', result[:message])
    else
      broadcast_result('alert', result[:message])
    end
  end

  def start_container(container_id)
    container = DockerContainer.find(container_id)
    return broadcast_result('alert', "Container not found") unless container

    if container.start
      broadcast_result('notice', "Container #{container.name} started successfully")
    else
      broadcast_result('alert', "Failed to start container #{container.name}: #{container.errors.full_messages.join(', ')}")
    end
  end

  def stop_container(container_id)
    container = DockerContainer.find(container_id)
    return broadcast_result('alert', "Container not found") unless container

    if container.stop
      broadcast_result('notice', "Container #{container.name} stopped successfully")
    else
      broadcast_result('alert', "Failed to stop container #{container.name}: #{container.errors.full_messages.join(', ')}")
    end
  end

  def restart_container(container_id)
    container = DockerContainer.find(container_id)
    return broadcast_result('alert', "Container not found") unless container

    if container.restart
      broadcast_result('notice', "Container #{container.name} restarted successfully")
    else
      broadcast_result('alert', "Failed to restart container #{container.name}: #{container.errors.full_messages.join(', ')}")
    end
  end

  def destroy_container(container_id)
    container = DockerContainer.find(container_id)
    return broadcast_result('alert', "Container not found") unless container

    if container.delete
      broadcast_result('notice', "Container #{container.name} deleted successfully")
    else
      broadcast_result('alert', "Failed to delete container #{container.name}: #{container.errors.full_messages.join(', ')}")
    end
  end


end
