class ApplicationJob < ActiveJob::Base
  # Automatically retry jobs that encountered a deadlock
  # retry_on ActiveRecord::Deadlocked

  # Most jobs are safe to ignore if the underlying records are no longer available
  # discard_on ActiveJob::DeserializationError


  protected

  def broadcast_result(type, message)
    Turbo::StreamsChannel.broadcast_update_to(
      "user_#{@user.id}_flash",
      target: 'flash_messages',
      partial: 'shared/flash_message',
      locals: { type: type, message: message }
    )
  end
end
