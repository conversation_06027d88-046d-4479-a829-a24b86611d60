class AutoRefreshTokenJob < ApplicationJob
  queue_as :default

  def perform
    Rails.logger.info("Starting auto refresh token job")

    # Find all cTrader accounts with auto refresh enabled and tokens expiring within 48 hours
    ctrader_accounts = CtraderAccount.joins(:account)
                                   .where(auto_refresh_token: true)
                                   .select { |account| account.token_expires_within?(48) }

    Rails.logger.info("Found #{ctrader_accounts.count} cTrader accounts that need token refresh")

    ctrader_accounts.each do |ctrader_account|
      begin
        process_account_token_refresh(ctrader_account)
      rescue => e
        Rails.logger.error("Error processing auto refresh for account #{ctrader_account.account_number}: #{e.message}")
        Rails.logger.error(e.backtrace.join("\n"))
      end
    end

    Rails.logger.info("Auto refresh token job completed")
  end

  private

  def process_account_token_refresh(ctrader_account)
    account = ctrader_account.account
    Rails.logger.info("Processing auto refresh for cTrader account #{ctrader_account.account_number} (Account ID: #{account.id})")

    # Find all scripts/bots that use this account
    scripts_using_account = find_scripts_using_account(account)

    Rails.logger.info("Found #{scripts_using_account.count} scripts using this account")

    # Stop all running scripts that use this account
    running_scripts = []
    scripts_using_account.each do |script|
      if script_running?(script)
        Rails.logger.info("Stopping script #{script.id} (#{script.type})")
        if stop_script(script)
          running_scripts << script
          Rails.logger.info("Successfully stopped script #{script.id}")
        else
          Rails.logger.error("Failed to stop script #{script.id}")
        end
      end
    end

    # Refresh the token
    Rails.logger.info("Refreshing token for account #{ctrader_account.account_number}")
    if ctrader_account.refresh_access_token
      Rails.logger.info("Successfully refreshed token for account #{ctrader_account.account_number}")

      # Restart the scripts that were running
      running_scripts.each do |script|
        Rails.logger.info("Restarting script #{script.id} (#{script.type})")
        if start_script(script)
          Rails.logger.info("Successfully restarted script #{script.id}")
        else
          Rails.logger.error("Failed to restart script #{script.id}")
        end
      end
    else
      Rails.logger.error("Failed to refresh token for account #{ctrader_account.account_number}")

      # If token refresh failed, try to restart the scripts anyway
      running_scripts.each do |script|
        Rails.logger.info("Attempting to restart script #{script.id} despite token refresh failure")
        start_script(script)
      end
    end
  end

  def find_scripts_using_account(account)
    # Find scripts where this account is the from_account (host account)
    scripts_as_host = Script.where(from_account: account)

    # Find copier scripts where this account is in the to_accounts
    scripts_as_target = Copier.joins(:to_accounts).where(accounts: { id: account.id })

    # Combine and return unique scripts
    (scripts_as_host + scripts_as_target).uniq
  end

  def script_running?(script)
    begin
      response = ScriptManagerService.script_status(script.id)
      return response && response.success? && response.parsed_response&.dig('status') == 'running'
    rescue => e
      Rails.logger.error("Error checking script status for script #{script.id}: #{e.message}")
      return false
    end
  end

  def stop_script(script)
    begin
      response = ScriptManagerService.stop_script(script.id)
      return response && response.success?
    rescue => e
      Rails.logger.error("Error stopping script #{script.id}: #{e.message}")
      return false
    end
  end

  def start_script(script)
    begin
      response = ScriptManagerService.start_script(script.id)
      return response && response.success?
    rescue => e
      Rails.logger.error("Error starting script #{script.id}: #{e.message}")
      return false
    end
  end
end
