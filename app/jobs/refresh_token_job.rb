class RefreshTokenJob < ApplicationJob
  queue_as :default

  def perform(account_id, user_id)
    @account = Account.find_by(id: account_id)
    @user = User.find_by(id: user_id)

    return unless @account && @user

    # Ensure the account belongs to the user
    return unless @account.user_id == @user.id

    # Ensure this is a CTrader account
    return unless @account.accountable_type == 'CtraderAccount'

    ctrader_account = @account.accountable

    # Attempt to refresh the token
    if ctrader_account.refresh_access_token
      # Broadcast success message
      broadcast_result('notice', "Token for #{@account.name || 'CTrader Account'} was successfully refreshed.")
    else
      # Broadcast error message
      broadcast_result('alert', "Failed to refresh token for #{@account.name || 'CTrader Account'}. Please try getting a new token.")
    end
  end
end
