require 'time'
require 'fileutils'

class DockerContainer
  include ActiveModel::Model
  include ActiveModel::Attributes
  include ActiveModel::Validations

  attribute :id, :string
  attribute :name, :string
  attribute :image, :string
  attribute :status, :string
  attribute :created_at, :datetime
  attribute :ports, :string
  attribute :command, :string
  attribute :network, :string

  class << self
    def all
      containers = Docker::Container.all(all: true)

      # Return an empty array if no containers exist
      return [] if containers.nil? || containers.empty?

      containers.map do |container|
        new_from_docker(container)
      end
    rescue => e
      Rails.logger.error("Error fetching Docker containers: #{e.message}")
      []
    end

    def find(id)
      # Return nil if id is blank
      return nil if id.blank?

      begin
        container = Docker::Container.get(id)
        return nil if container.nil?
        new_from_docker(container)
      rescue Docker::Error::NotFoundError
        Rails.logger.error("Docker container #{id} not found")
        nil
      end
    end

    def parse_created_time(created)
      if created.is_a?(Numeric) || created.to_s.match?(/^\d+$/)
        # Handle Unix timestamp (numeric value)
        Time.at(created.to_i)
      elsif created.is_a?(String) && created.match?(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/)
        # Handle ISO 8601 format string like "2025-03-09T16:53:38.969365242Z"
        Time.parse(created)
      else
        # Fallback to current time if format is unknown
        Rails.logger.warn("Unknown time format: #{created}")
        Time.now
      end
    rescue => e
      Rails.logger.error("Error parsing created time: #{e.message}")
      Time.now
    end

    def new_from_docker(container)
      info = container.info

      # Format ports information
      ports_info = []

      # Check if NetworkSettings.Ports exists and has data
      if info['NetworkSettings'] && info['NetworkSettings']['Ports']
        info['NetworkSettings']['Ports'].each do |container_port, host_bindings|
          if host_bindings.nil? || host_bindings.empty?
            ports_info << container_port
          else
            host_bindings.each do |binding|
              ports_info << "#{binding['HostIp']}:#{binding['HostPort']}->#{container_port}"
            end
          end
        end
      # Fallback to old method if NetworkSettings.Ports is not available
      elsif info['Ports']
        info['Ports'].each do |port|
          if port['PublicPort']
            ports_info << "#{port['IP']}:#{port['PublicPort']}->#{port['PrivatePort']}/#{port['Type']}"
          else
            ports_info << "#{port['PrivatePort']}/#{port['Type']}"
          end
        end
      end

      ports = ports_info.join(', ')

      # Get network information
      network = 'default'

      # Try to get network from HostConfig
      if info['HostConfig'] && info['HostConfig']['NetworkMode']
        network = info['HostConfig']['NetworkMode']
      # Fallback to NetworkSettings if available
      elsif info['NetworkSettings'] && info['NetworkSettings']['Networks']
        # Get the first network name
        network = info['NetworkSettings']['Networks'].keys.first if info['NetworkSettings']['Networks'].keys.any?
      end

      # For containers using the default bridge network, show 'bridge'
      network = 'bridge' if network == 'default'

      # Get image name from Config.Image if available, otherwise use the Image hash
      image = info['Config'] && info['Config']['Image'] ? info['Config']['Image'] : info['Image']

      # Get status from State.Status if available, otherwise use State directly
      status = info['State'].is_a?(Hash) && info['State']['Status'] ? info['State']['Status'] : info['State']

      # Get command from Config.Cmd if available, otherwise use Command
      command = info['Config'] && info['Config']['Cmd'] ? info['Config']['Cmd'].join(' ') : info['Command']

      # Get name, removing leading slash if present
      name = info['Name'] ? info['Name'].sub(/^\//, '') :
             (info['Names'] && info['Names'].first ? info['Names'].first.sub(/^\//, '') : 'unknown')

      new(
        id: container.id,
        name: name,
        image: image,
        status: status,
        created_at: parse_created_time(info['Created']),
        ports: ports,
        command: command,
        network: network
      )
    end
  end

  def start
    container = Docker::Container.get(id)
    container.start
    true
  rescue => e
    Rails.logger.error("Error starting Docker container #{id}: #{e.message}")
    errors.add(:base, "Failed to start container: #{e.message}")
    false
  end

  def stop
    container = Docker::Container.get(id)
    container.stop
    true
  rescue => e
    Rails.logger.error("Error stopping Docker container #{id}: #{e.message}")
    errors.add(:base, "Failed to stop container: #{e.message}")
    false
  end

  def delete
    container = Docker::Container.get(id)
    container_name = name

    # Delete the container
    container.delete(force: true)
    system('docker system prune -f')

    true
  rescue => e
    Rails.logger.error("Error deleting Docker container #{id}: #{e.message}")
    errors.add(:base, "Failed to delete container: #{e.message}")
    false
  end

  def restart
    container = Docker::Container.get(id)
    container.restart
    true
  rescue => e
    Rails.logger.error("Error restarting Docker container #{id}: #{e.message}")
    errors.add(:base, "Failed to restart container: #{e.message}")
    false
  end
end
