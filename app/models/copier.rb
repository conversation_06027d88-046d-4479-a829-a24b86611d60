class Copier < Script
  has_and_belongs_to_many :to_accounts, class_name: 'Account', join_table: 'accounts_scripts', foreign_key: 'script_id'

  after_create :init_config

  # Initialize the base configuration for the copier script
  def init_config
    base_config = {
      CLIENT_ID: ENV['CLIENT_ID_CTRADER_APP'],
      CLIENT_SECRET: ENV['CLIENT_SECRET_CTRADER_APP'],
      CONNECTION_STRING: ENV['INTERNAL_CONNECTION_STRING'],
      MT5_URL: ENV['MT5_URL_DEDAULT'],
      FROM_ACCOUNT_ID: from_account_id
    }

    # Add account-specific configuration
    update_config(base_config)
  end

  # Update the configuration with the latest account information
  # Optionally merge with additional custom configuration
  def update_config(custom_config = {})
    # Merge with base config and any custom config
    final_config = get_config.merge(custom_config)

    # Save the configuration to the database
    update(config: final_config.to_json)

    # Return the final configuration
    final_config
  end

  # Get the current configuration as a hash
  def get_config
    return {} unless config.present?

    begin
      JSON.parse(config).symbolize_keys
    rescue JSON::ParserError
      {}
    end
  end
end
