class Script < ApplicationRecord
  belongs_to :from_account, class_name: 'Account', foreign_key: 'from_account_id'
  belongs_to :user

  has_many :deals, dependent: :destroy

  validates :from_account, presence: true

  # Enum for expect_status
  enum :expect_status, stopped: 0, running: 1, error: -1

  # Scope to get scripts that should be running
  scope :expect_running, -> { where(expect_status: :running) }

end
