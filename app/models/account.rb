class Account < ApplicationRecord
  belongs_to :user
  delegated_type :accountable, types: ['Mt5Account', 'CtraderAccount']

  scope :mt5, -> { where(accountable_type: 'Mt5Account') }
  scope :ctrader, -> { where(accountable_type: 'CtraderAccount') }

  def display_name
    if name.present?
      "#{name} (#{account_type_info})"
    else
      account_type_info
    end
  end

  private

  def account_type_info
    if accountable_type == 'Mt5Account'
      "MT5: #{accountable.login} - #{accountable.server}"
    elsif accountable_type == 'CtraderAccount'
      "CTrader: #{accountable.account_id} - #{accountable.host_type}"
    else
      "Unknown Account Type"
    end
  end
end
