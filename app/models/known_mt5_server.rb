class KnownMt5Server < ApplicationRecord
  validates :mt5_url, presence: true, uniqueness: true

  # Add a server name to the array if it doesn't already exist
  def add_server_name(name)
    return if name.blank? || server_name.include?(name)

    self.server_name = server_name + [name]
    save!
  end

  # Remove a server name from the array
  def remove_server_name(name)
    return unless server_name.include?(name)

    self.server_name = server_name - [name]
    save!
  end

  # Check if a server name exists in the array
  def has_server_name?(name)
    server_name.include?(name)
  end
end
