class AccountsController < ApplicationController
  before_action :authenticate_user!
  before_action :set_account, only: [:show, :edit, :update, :destroy, :get_ctrader_token, :refresh_ctrader_token]

  require 'net/http'
  require 'uri'
  require 'json'

  def index
    @accounts = current_user.accounts
  end

  def show
  end

  def new
    @account = Account.new
    @account_type = params[:type] || 'mt5'
  end

  def edit
  end

  def create
    @account_type = account_params[:accountable_type].downcase

    if @account_type == 'mt5account'
      @accountable = Mt5Account.new(
        login: account_params[:login],
        password: account_params[:password],
        server: account_params[:server]
      )
    elsif @account_type == 'ctraderaccount'
      # For CTrader accounts, we only require the account_number initially
      # The account_id and host_type will be set when the user gets the token
      @accountable = CtraderAccount.new(
        account_number: account_params[:account_number],
        host_type: nil, # Default to demo until we get the actual type from the API
        account_id: nil,
        auto_refresh_token: account_params[:auto_refresh_token] || true
      )
    end

    if @accountable.save
      @account = current_user.accounts.build(
        name: account_params[:name],
        accountable: @accountable
      )

      if @account.save
        if @account_type == 'ctraderaccount'
          # For CTrader accounts, redirect to get token immediately
          redirect_to accounts_path, notice: 'Account created. Please click Get Token to complete setup.'
        else
          redirect_to accounts_path, notice: 'Account was successfully created.'
        end
      else
        @accountable.destroy
        render :new, status: :unprocessable_entity
      end
    else
      render :new, status: :unprocessable_entity
    end
  end

  def update
    if @account.update(account_params.slice(:name))
      if @account.accountable_type == 'Mt5Account'
        @account.accountable.update(
          login: account_params[:login],
          password: account_params[:password],
          server: account_params[:server],
          volume_leverage: account_params[:volume_leverage]
        )
      elsif @account.accountable_type == 'CtraderAccount'
        # For CTrader accounts, we can update account_number and auto_refresh_token
        ctrader_params = {}
        ctrader_params[:account_number] = account_params[:account_number] if account_params[:account_number].present?
        ctrader_params[:auto_refresh_token] = account_params[:auto_refresh_token] if account_params.key?(:auto_refresh_token)

        if ctrader_params.any?
          @account.accountable.update(ctrader_params)

          # If the account number has changed, redirect to get a new token
          if account_params[:account_number].present? && @account.accountable.account_number.to_s != account_params[:account_number].to_s
            redirect_to accounts_path, notice: 'Account number updated. Please click Get Token to complete setup.'
            return
          end
        end
      end
      redirect_to accounts_path, notice: 'Account was successfully updated.'
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @account.destroy
    redirect_to accounts_path, notice: 'Account was successfully deleted.'
  end

  def get_ctrader_token
    # Store the account ID in the session for retrieval after OAuth callback
    session[:ctrader_account_id] = @account.id

    # If this is an existing CTrader account with an account number, store it in the session
    if @account.accountable_type == 'CtraderAccount' && @account.accountable.account_number.present?
      session[:ctrader_account_number] = @account.accountable.account_number
    end

    # Build the authorization URL with required parameters
    client_id = ENV['CLIENT_ID_CTRADER_APP']
    redirect_uri = ctraderauth_url
    scope = 'trading'

    # Use the new authorization URL format provided by cTrader
    auth_url = "https://id.ctrader.com/my/settings/openapi/grantingaccess/?client_id=#{client_id}&redirect_uri=#{redirect_uri}&scope=#{scope}&product=web"

    # Redirect the user to cTrader's authorization page
    redirect_to auth_url, allow_other_host: true
  end

  def refresh_ctrader_token
    # Ensure this is a CTrader account
    unless @account.accountable_type == 'CtraderAccount'
      redirect_to accounts_path, alert: 'This action is only available for CTrader accounts.'
      return
    end

    # Queue the job to refresh the token
    RefreshTokenJob.perform_later(@account.id, current_user.id)

    # Render Turbo Stream response directly for immediate feedback
    render turbo_stream: turbo_stream.update("flash_messages",
      partial: "shared/flash_message",
      locals: { type: 'notice', message: 'Request received. Refreshing token...' }
    )
  end

  def ctrader_callback
    # Check if we have an authorization code
    if params[:code].present?
      # Get the account ID from the session
      account_id = session[:ctrader_account_id]
      account_number = session[:ctrader_account_number]

      if account_id.present?
        account = Account.find_by(id: account_id)

        if account && account.user.present?

          # Exchange the authorization code for an access token
          token_data = exchange_code_for_token(params[:code])

          if token_data && token_data['access_token'].present?
            # Get the trading accounts using the access token
            trading_accounts = get_ctrader_trading_accounts(token_data['access_token'])

            if trading_accounts.present?
              # If we have an account number in the session, find that specific account
              # Otherwise, we'll use the first account in the list
              selected_account = nil

              if account.accountable_type == 'CtraderAccount' && account.accountable.account_number.present?
                # Try to find the account with the matching account number
                selected_account = trading_accounts.find { |acc| acc['accountNumber'].to_s == account.accountable.account_number.to_s }
              elsif account_number.present?
                # Try to find the account with the matching account number from session
                selected_account = trading_accounts.find { |acc| acc['accountNumber'].to_s == account_number.to_s }
              end

              if selected_account
                # Determine the host type based on the 'live' flag
                host_type = selected_account['live'] ? 'live' : 'demo'

                # Update or create the CtraderAccount
                if account.accountable_type == 'CtraderAccount'
                  # Update existing CtraderAccount
                  account.accountable.update(
                    account_id: selected_account['accountId'],
                    account_number: selected_account['accountNumber'],
                    host_type: host_type,
                    access_token: token_data['access_token'],
                    refresh_token: token_data['refresh_token'],
                    expires_in: token_data['expires_in']
                  )
                  flash[:notice] = 'CTrader account was successfully updated with new token.'
                else
                  # Create a new CtraderAccount
                  ctrader_account = CtraderAccount.create!(
                    account_id: selected_account['accountId'],
                    account_number: selected_account['accountNumber'],
                    host_type: host_type,
                    access_token: token_data['access_token'],
                    refresh_token: token_data['refresh_token'],
                    expires_in: token_data['expires_in']
                  )

                  # Update the account to use the new CtraderAccount
                  account.update(accountable: ctrader_account)
                  flash[:notice] = 'CTrader account was successfully created.'
                end

                # Clear the session data
                session.delete(:ctrader_account_id)
                session.delete(:ctrader_account_number)

                # Redirect to the accounts page
                redirect_to accounts_path
                return
              else
                flash[:alert] = 'No matching trading account found.'
              end
            else
              flash[:alert] = 'No trading accounts found for this user.'
            end
          else
            flash[:alert] = 'Failed to exchange authorization code for access token.'
          end
        else
          flash[:alert] = 'Account not found or user not authenticated.'
        end
      else
        flash[:alert] = 'No account ID found in session.'
      end
    else
      flash[:alert] = 'No authorization code received from cTrader.'
    end

    # If we get here, something went wrong
    redirect_to accounts_path
  end

  private

  def set_account
    @account = current_user.accounts.find(params[:id])
  end

  def account_params
    params.require(:account).permit(
      :name, :accountable_type,
      :login, :password, :server, :volume_leverage, # MT5 params
      :account_id, :account_number, :host_type, :access_token, :refresh_token, :expires_in, :auto_refresh_token # CTrader params
    )
  end

  def exchange_code_for_token(code)
    # Use the cTrader token endpoint
    token_url = "https://openapi.ctrader.com/apps/token"

    # Set up the request parameters as query parameters
    query_params = {
      grant_type: 'authorization_code',
      code: code,
      client_id: ENV['CLIENT_ID_CTRADER_APP'],
      client_secret: ENV['CLIENT_SECRET_CTRADER_APP'],
      redirect_uri: ctraderauth_url
    }

    # Make the GET request to exchange the code for a token
    begin
      uri = URI.parse(token_url)
      uri.query = URI.encode_www_form(query_params)
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true

      request = Net::HTTP::Get.new(uri.request_uri)
      request['Accept'] = 'application/json'
      request['Content-Type'] = 'application/json'

      response = http.request(request)

      Rails.logger.info(response.body)
      if response.code == '200'
        # Parse the JSON response
        token_data = JSON.parse(response.body)
        return token_data
      else
        Rails.logger.error("Failed to exchange code for token: #{response.body}")
        return nil
      end
    rescue => e
      Rails.logger.error("Error exchanging code for token: #{e.message}")
      return nil
    end
  end

  def get_ctrader_account_info(access_token)
    # Use the cTrader API endpoint for user profile
    api_url = "https://id.ctrader.com/api/v1/user/profile"

    # Make the GET request to retrieve account information
    begin
      uri = URI.parse(api_url)
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true

      request = Net::HTTP::Get.new(uri.path)
      request['Authorization'] = "Bearer #{access_token}"

      response = http.request(request)

      if response.code == '200'
        # Parse the JSON response
        account_data = JSON.parse(response.body)

        # Extract the account ID from the response
        # The structure of the response from cTrader API
        if account_data['id']
          return { 'account_id' => account_data['id'] }
        else
          Rails.logger.error("No account ID found in response: #{response.body}")
          return nil
        end
      else
        Rails.logger.error("Failed to get account info: #{response.body}")
        return nil
      end
    rescue => e
      Rails.logger.error("Error getting account info: #{e.message}")
      return nil
    end
  end

  def get_ctrader_trading_accounts(access_token)
    # Use the Spotware API endpoint for trading accounts
    api_url = "https://api.spotware.com/connect/tradingaccounts"

    # Make the GET request to retrieve trading accounts
    begin
      uri = URI.parse("#{api_url}?access_token=#{access_token}")
      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true

      request = Net::HTTP::Get.new(uri.request_uri)

      response = http.request(request)

      if response.code == '200'
        # Parse the JSON response
        trading_accounts_data = JSON.parse(response.body)

        if trading_accounts_data['data'] && trading_accounts_data['data'].is_a?(Array)
          return trading_accounts_data['data']
        else
          Rails.logger.error("No trading accounts found in response: #{response.body}")
          return []
        end
      else
        Rails.logger.error("Failed to get trading accounts: #{response.body}")
        return []
      end
    rescue => e
      Rails.logger.error("Error getting trading accounts: #{e.message}")
      return []
    end
  end
end
