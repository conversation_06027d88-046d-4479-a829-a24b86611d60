class Api::V1::ScriptController < ApplicationController
  before_action :authenticate_user!
  before_action :set_script, only: [:start, :stop, :status]

  def start
    response = ScriptManagerService.start_script(@script.id)
    render json: response
  end

  def stop
    response = ScriptManagerService.stop_script(@script.id)
    render json: response
end

  def status
    response = ScriptManagerService.script_status(@script.id)
    render json: response
  end

  private

  def set_script
    @script = current_user.scripts.find_by(id: params[:id])
    unless @script
      render json: { error: 'Script not found' }, status: :not_found
    end
  end
end
