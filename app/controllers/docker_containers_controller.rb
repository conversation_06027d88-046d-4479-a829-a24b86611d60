class DockerContainersController < ApplicationController
  layout 'admin'
  before_action :set_container, only: [:show, :start, :stop, :restart, :destroy]

  def index
    begin
      @containers = DockerContainer.all
    rescue => e
      Rails.logger.error("Error loading Docker containers: #{e.message}")
      @containers = []
      flash.now[:alert] = "Error loading Docker containers: #{e.message}"
    end
  end

  def show
    # Container is already set by the before_action
    # If we get here, it means the container was found successfully
    # No additional logic needed
  rescue => e
    Rails.logger.error("Error showing Docker container: #{e.message}")
    redirect_to docker_containers_path, alert: "Error showing Docker container: #{e.message}"
  end

  def start
    # Queue the job to start the container
    DockerContainerJob.perform_later('start_container', current_user.id, @container.id)

    # Render Turbo Stream response directly for immediate feedback
    render turbo_stream: turbo_stream.update("flash_messages",
      partial: "shared/flash_message",
      locals: { type: 'notice', message: "Request received. Starting container #{@container.name}..." }
    )
  end

  def stop
    # Queue the job to stop the container
    DockerContainerJob.perform_later('stop_container', current_user.id, @container.id)

    # Render Turbo Stream response directly for immediate feedback
    render turbo_stream: turbo_stream.update("flash_messages",
      partial: "shared/flash_message",
      locals: { type: 'notice', message: "Request received. Stopping container #{@container.name}..." }
    )
  end

  def restart
    # Queue the job to restart the container
    DockerContainerJob.perform_later('restart_container', current_user.id, @container.id)

    # Render Turbo Stream response directly for immediate feedback
    render turbo_stream: turbo_stream.update("flash_messages",
      partial: "shared/flash_message",
      locals: { type: 'notice', message: "Request received. Restarting container #{@container.name}..." }
    )
  end

  def destroy
    # Queue the job to destroy the container
    DockerContainerJob.perform_later('destroy_container', current_user.id, @container.id)

    # Render Turbo Stream response directly for immediate feedback
    render turbo_stream: turbo_stream.update("flash_messages",
      partial: "shared/flash_message",
      locals: { type: 'notice', message: "Request received. Destroying container #{@container.name}..." }
    )
  end

  def clone_celery_worker
    name_suffix = params[:name_suffix]
    host_port = params[:host_port].presence # Will be nil if blank

    if name_suffix.blank?
      redirect_to docker_containers_path, alert: "Name suffix cannot be blank"
      return
    end

    # Validate host port if provided
    if host_port.present?
      begin
        host_port = Integer(host_port)
        if host_port < 1024 || host_port > 65535
          redirect_to docker_containers_path, alert: "Host port must be between 1024 and 65535"
          return
        end
      rescue ArgumentError
        redirect_to docker_containers_path, alert: "Host port must be a valid number"
        return
      end
    end

    # Call the service with the host port
    result = DockerService.clone_celery_worker(name_suffix, host_port)

    if result[:success]
      redirect_to docker_containers_path, notice: result[:message]
    else
      redirect_to docker_containers_path, alert: result[:message]
    end
  end

  def init_base_services
    # Queue the job to initialize base services
    DockerContainerJob.perform_later('init_base_services', current_user.id)

    # Render Turbo Stream response directly for immediate feedback
    render turbo_stream: turbo_stream.update("flash_messages",
      partial: "shared/flash_message",
      locals: { type: 'notice', message: 'Request received. Initializing base services...' }
    )
  end

  def destroy_all_containers
    if params[:confirm] != 'true'
      redirect_to docker_containers_path, alert: "Please confirm that you want to destroy all containers"
      return
    end

    result = DockerService.destroy_all_containers

    if result[:success]
      redirect_to docker_containers_path, notice: result[:message]
    else
      redirect_to docker_containers_path, alert: result[:message]
    end
  end

  def add_mt5_server
    server_name = params[:server_name]
    container_name = params[:container_name]

    if server_name.blank?
      redirect_to docker_containers_path, alert: "Server name cannot be blank"
      return
    end

    result = DockerService.add_mt5_server(server_name, container_name)

    if result[:success]
      redirect_to docker_containers_path, notice: result[:message]
    else
      redirect_to docker_containers_path, alert: result[:message]
    end
  end

  private

  def set_container
    # Check if ID is present
    if params[:id].blank?
      redirect_to docker_containers_path, alert: "Container ID is missing"
      return
    end

    # Try to find the container
    @container = DockerContainer.find(params[:id])

    # Redirect if container not found
    unless @container
      redirect_to docker_containers_path, alert: "Container not found"
      return
    end
  end
end
