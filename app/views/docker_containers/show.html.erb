<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Container Details: <%= @container.name %></h1>
    <%= link_to "Back to Containers", docker_containers_path, class: "bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded" %>
  </div>

  <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Container Information</h2>
        <table class="w-full">
          <tr class="border-b dark:border-gray-700">
            <td class="py-2 font-semibold text-gray-900 dark:text-white">ID:</td>
            <td class="py-2 text-gray-900 dark:text-white"><%= @container.id %></td>
          </tr>
          <tr class="border-b dark:border-gray-700">
            <td class="py-2 font-semibold text-gray-900 dark:text-white">Name:</td>
            <td class="py-2 text-gray-900 dark:text-white"><%= @container.name %></td>
          </tr>
          <tr class="border-b dark:border-gray-700">
            <td class="py-2 font-semibold text-gray-900 dark:text-white">Image:</td>
            <td class="py-2 text-gray-900 dark:text-white"><%= @container.image %></td>
          </tr>
          <tr class="border-b dark:border-gray-700">
            <td class="py-2 font-semibold text-gray-900 dark:text-white">Status:</td>
            <td class="py-2">
              <% if @container.status == 'running' %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">
                  <%= @container.status %>
                </span>
              <% elsif @container.status == 'exited' %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">
                  <%= @container.status %>
                </span>
              <% else %>
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">
                  <%= @container.status %>
                </span>
              <% end %>
            </td>
          </tr>
          <tr class="border-b dark:border-gray-700">
            <td class="py-2 font-semibold text-gray-900 dark:text-white">Created:</td>
            <td class="py-2 text-gray-900 dark:text-white"><%= @container.created_at.strftime("%Y-%m-%d %H:%M:%S") %></td>
          </tr>
          <tr class="border-b dark:border-gray-700">
            <td class="py-2 font-semibold text-gray-900 dark:text-white">Network:</td>
            <td class="py-2">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                <%= @container.network %>
              </span>
            </td>
          </tr>
          <tr class="border-b dark:border-gray-700">
            <td class="py-2 font-semibold text-gray-900 dark:text-white">Ports:</td>
            <td class="py-2 text-gray-900 dark:text-white"><%= @container.ports %></td>
          </tr>
          <tr>
            <td class="py-2 font-semibold text-gray-900 dark:text-white">Command:</td>
            <td class="py-2 text-gray-900 dark:text-white"><%= @container.command %></td>
          </tr>
        </table>
      </div>

      <div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">Actions</h2>
        <div class="space-y-3">
          <% if @container.status == 'running' %>
            <%= button_to "Stop Container", stop_docker_container_path(@container), method: :post, class: "w-full bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded" %>
            <%= button_to "Restart Container", restart_docker_container_path(@container), method: :post, class: "w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" %>
          <% else %>
            <%= button_to "Start Container", start_docker_container_path(@container), method: :post, class: "w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded" %>
          <% end %>
          <%= button_to "Delete Container", docker_container_path(@container), method: :delete, class: "w-full bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded", data: { confirm: "Are you sure you want to delete this container?" } %>
        </div>
      </div>
    </div>
  </div>
</div>
