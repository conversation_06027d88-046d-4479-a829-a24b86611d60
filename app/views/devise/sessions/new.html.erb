<div class="flex min-h-full flex-col justify-center px-6 py-12 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-sm">
    <h2 class="mt-10 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900 dark:text-white">Sign in to your account</h2>
  </div>

  <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
    <%= form_for(resource, as: resource_name, url: session_path(resource_name), html: { class: "space-y-6" }) do |f| %>
      <div>
        <%= f.label :email, class: "block text-sm font-medium leading-6 text-gray-900 dark:text-white" %>
        <div class="mt-2">
          <%= f.email_field :email, autofocus: true, autocomplete: "email", class: "block w-full rounded-md border-0 bg-white dark:bg-gray-700 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-inset focus:ring-indigo-600 dark:focus:ring-indigo-500 sm:text-sm sm:leading-6" %>
        </div>
      </div>

      <div>
        <div class="flex items-center justify-between">
          <%= f.label :password, class: "block text-sm font-medium leading-6 text-gray-900 dark:text-white" %>
          <% if devise_mapping.recoverable? %>
            <div class="text-sm">
              <%= link_to "Forgot your password?", new_password_path(resource_name), class: "font-semibold text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300" %>
            </div>
          <% end %>
        </div>
        <div class="mt-2">
          <%= f.password_field :password, autocomplete: "current-password", class: "block w-full rounded-md border-0 bg-white dark:bg-gray-700 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-inset focus:ring-indigo-600 dark:focus:ring-indigo-500 sm:text-sm sm:leading-6" %>
        </div>
      </div>

      <% if devise_mapping.rememberable? %>
        <div class="flex items-center">
          <%= f.check_box :remember_me, class: "h-4 w-4 rounded bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-indigo-600 focus:ring-indigo-600 dark:focus:ring-indigo-500" %>
          <%= f.label :remember_me, class: "ml-2 block text-sm text-gray-700 dark:text-gray-300" %>
        </div>
      <% end %>

      <div>
        <%= f.submit "Sign in", class: "flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
      </div>
    <% end %>

    <div class="mt-6">
      <%= render "devise/shared/links" %>
    </div>
  </div>
</div>
