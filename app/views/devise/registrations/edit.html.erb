<div class="container mx-auto px-4 py-8">
  <div class="bg-gray-100 dark:bg-gray-800 rounded-lg shadow-lg p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Edit Profile</h1>
      <p class="text-gray-600 dark:text-gray-400">Update your account information</p>
    </div>

    <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { method: :put, class: "space-y-6" }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>

      <div>
        <%= f.label :name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
        <%= f.text_field :name, class: "w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600" %>
      </div>

      <div>
        <%= f.label :email, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
        <%= f.email_field :email, autofocus: true, autocomplete: "email", class: "w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600" %>
      </div>

      <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
        <div class="p-3 bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-200 rounded-md">
          Currently waiting confirmation for: <%= resource.unconfirmed_email %>
        </div>
      <% end %>

      <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Change Password</h2>
        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Leave blank if you don't want to change it</p>

        <div class="space-y-4">
          <div>
            <%= f.label :password, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
            <%= f.password_field :password, autocomplete: "new-password", class: "w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600" %>
            <% if @minimum_password_length %>
              <p class="mt-1 text-xs text-gray-500 dark:text-gray-400"><%= @minimum_password_length %> characters minimum</p>
            <% end %>
          </div>

          <div>
            <%= f.label :password_confirmation, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
            <%= f.password_field :password_confirmation, autocomplete: "new-password", class: "w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600" %>
          </div>
        </div>
      </div>

      <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
        <div>
          <%= f.label :current_password, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1" %>
          <p class="text-sm text-gray-500 dark:text-gray-400 mb-2">We need your current password to confirm your changes</p>
          <%= f.password_field :current_password, autocomplete: "current-password", class: "w-full bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md py-2 px-3 text-gray-700 dark:text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-600" %>
        </div>
      </div>

      <div class="flex justify-end">
        <%= f.submit "Update", class: "px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2" %>
      </div>
    <% end %>

    <div class="border-t border-gray-200 dark:border-gray-700 pt-6 mt-6">
      <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Delete Account</h2>
      <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">Once you delete your account, all of your data will be permanently removed.</p>

      <div class="flex items-center justify-between">
        <%= link_to "Back", :back, class: "text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white" %>
        <%= button_to "Delete Account", registration_path(resource_name),
            method: :delete,
            data: { confirm: "Are you sure you want to delete your account? This action cannot be undone.", turbo_confirm: "Are you sure you want to delete your account? This action cannot be undone." },
            class: "px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2" %>
      </div>
    </div>
  </div>
</div>
