<div class="flex min-h-full flex-col justify-center px-6 py-12 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-sm">
    <h2 class="mt-10 text-center text-2xl font-bold leading-9 tracking-tight text-gray-900 dark:text-white">Sign up</h2>
  </div>

  <div class="mt-10 sm:mx-auto sm:w-full sm:max-w-sm">
    <%= form_for(resource, as: resource_name, url: registration_path(resource_name), html: { class: "space-y-6" }) do |f| %>
      <%= render "devise/shared/error_messages", resource: resource %>

      <div>
        <%= f.label :name, class: "block text-sm font-medium leading-6 text-gray-900 dark:text-white" %>
        <div class="mt-2">
          <%= f.text_field :name, autofocus: true, class: "block w-full rounded-md border-0 bg-white dark:bg-gray-700 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-inset focus:ring-indigo-600 dark:focus:ring-indigo-500 sm:text-sm sm:leading-6" %>
        </div>
      </div>

      <div>
        <%= f.label :email, class: "block text-sm font-medium leading-6 text-gray-900 dark:text-white" %>
        <div class="mt-2">
          <%= f.email_field :email, autocomplete: "email", class: "block w-full rounded-md border-0 bg-white dark:bg-gray-700 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-inset focus:ring-indigo-600 dark:focus:ring-indigo-500 sm:text-sm sm:leading-6" %>
        </div>
      </div>

      <div>
        <%= f.label :password, class: "block text-sm font-medium leading-6 text-gray-900 dark:text-white" %>
        <div class="mt-2">
          <%= f.password_field :password, autocomplete: "new-password", class: "block w-full rounded-md border-0 bg-white dark:bg-gray-700 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-inset focus:ring-indigo-600 dark:focus:ring-indigo-500 sm:text-sm sm:leading-6" %>
        </div>
        <% if @minimum_password_length %>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400"><%= @minimum_password_length %> characters minimum</p>
        <% end %>
      </div>

      <div>
        <%= f.label :password_confirmation, class: "block text-sm font-medium leading-6 text-gray-900 dark:text-white" %>
        <div class="mt-2">
          <%= f.password_field :password_confirmation, autocomplete: "new-password", class: "block w-full rounded-md border-0 bg-white dark:bg-gray-700 py-1.5 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 focus:ring-2 focus:ring-inset focus:ring-indigo-600 dark:focus:ring-indigo-500 sm:text-sm sm:leading-6" %>
        </div>
      </div>

      <div>
        <%= f.submit "Sign up", class: "flex w-full justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600" %>
      </div>
    <% end %>

    <div class="mt-6">
      <%= render "devise/shared/links" %>
    </div>
  </div>
</div>
