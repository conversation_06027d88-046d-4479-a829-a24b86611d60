<div class="text-sm text-gray-500 dark:text-gray-400 space-y-2">
  <%- if controller_name != 'sessions' %>
    <div>
      <%= link_to "Log in", new_session_path(resource_name), class: "font-semibold text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300" %>
    </div>
  <% end %>

  <%- if devise_mapping.registerable? && controller_name != 'registrations' %>
    <div>
      <%= link_to "Sign up", new_registration_path(resource_name), class: "font-semibold text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300" %>
    </div>
  <% end %>

  <%- if devise_mapping.recoverable? && controller_name != 'passwords' && controller_name != 'registrations' %>
    <div>
      <%= link_to "Forgot your password?", new_password_path(resource_name), class: "font-semibold text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300" %>
    </div>
  <% end %>

  <%- if devise_mapping.confirmable? && controller_name != 'confirmations' %>
    <div>
      <%= link_to "Didn't receive confirmation instructions?", new_confirmation_path(resource_name), class: "font-semibold text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300" %>
    </div>
  <% end %>

  <%- if devise_mapping.lockable? && resource_class.unlock_strategy_enabled?(:email) && controller_name != 'unlocks' %>
    <div>
      <%= link_to "Didn't receive unlock instructions?", new_unlock_path(resource_name), class: "font-semibold text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300" %>
    </div>
  <% end %>

  <%- if devise_mapping.omniauthable? %>
    <%- resource_class.omniauth_providers.each do |provider| %>
      <div>
        <%= button_to "Sign in with #{OmniAuth::Utils.camelize(provider)}", omniauth_authorize_path(resource_name, provider), data: { turbo: false }, class: "font-semibold text-indigo-600 dark:text-indigo-400 hover:text-indigo-500 dark:hover:text-indigo-300" %>
      </div>
    <% end %>
  <% end %>
</div>
