<div class="container mx-auto px-4 py-8">
  <div class="bg-gray-800 rounded-lg shadow-lg p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-white">
        <% if @bot.type == 'Copier' %>
          Copier Bot #<%= @bot.id %>
        <% else %>
          Trader Bot #<%= @bot.id %>
        <% end %>
      </h1>
      <p class="text-gray-400">
        Created on <%= @bot.created_at.strftime("%B %d, %Y") %>
      </p>
    </div>

    <div class="bg-gray-700 rounded-lg p-6 mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold text-white">Status</h2>
        <span class="bot-status px-2 py-1 text-sm rounded-full bg-gray-600 text-gray-200" data-bot-id="<%= @bot.id %>">
          Checking...
        </span>
      </div>

      <% if @bot.type == 'Copier' %>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
          <div>
            <h3 class="text-lg font-medium text-white mb-3">Source Account</h3>
            <% if @bot.from_account %>
              <div class="bg-gray-600 rounded-lg p-4">
                <p class="text-gray-300">
                  <span class="text-gray-400">Name:</span>
                  <%= @bot.from_account.name || "CTrader Account ##{@bot.from_account.id}" %>
                </p>
                <p class="text-gray-300">
                  <span class="text-gray-400">Account ID:</span>
                  <%= @bot.from_account.accountable.account_id %>
                </p>
                <p class="text-gray-300">
                  <span class="text-gray-400">Host Type:</span>
                  <%= @bot.from_account.accountable.host_type.capitalize %>
                </p>
                <p class="text-gray-300">
                  <span class="text-gray-400">Token Status:</span>
                  <% if @bot.from_account.accountable.access_token.present? %>
                    <span class="px-2 py-1 text-xs rounded-full bg-green-800 text-green-200">Active</span>
                  <% else %>
                    <span class="px-2 py-1 text-xs rounded-full bg-red-800 text-red-200">Missing</span>
                  <% end %>
                </p>
              </div>
            <% else %>
              <p class="text-gray-400">No source account selected</p>
            <% end %>
          </div>

          <div>
            <h3 class="text-lg font-medium text-white mb-3">Destination Accounts</h3>
            <% if @bot.to_accounts.any? %>
              <div class="space-y-3">
                <% @bot.to_accounts.each do |account| %>
                  <div class="bg-gray-600 rounded-lg p-4">
                    <p class="text-gray-300">
                      <span class="text-gray-400">Name:</span>
                      <%= account.name || "MT5 Account ##{account.id}" %>
                    </p>
                    <p class="text-gray-300">
                      <span class="text-gray-400">Login:</span>
                      <%= account.accountable.login %>
                    </p>
                    <p class="text-gray-300">
                      <span class="text-gray-400">Server:</span>
                      <%= account.accountable.server %>
                    </p>
                  </div>
                <% end %>
              </div>
            <% else %>
              <p class="text-gray-400">No destination accounts selected</p>
            <% end %>
          </div>
        </div>
      <% else %>
        <div class="bg-yellow-800 text-yellow-200 p-4 rounded-lg">
          <p class="font-medium">Trader bots are not yet supported.</p>
          <p class="mt-2">This feature will be available in a future update.</p>
        </div>
      <% end %>
    </div>

    <div class="flex space-x-4">
      <%= link_to "Back to Bots", bots_path, class: "bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700" %>
      <%= link_to "Edit Bot", edit_bot_path(@bot), class: "bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700" %>
      <%= button_to "Start Bot", start_bot_path(@bot), method: :post, class: "bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700" %>
      <%= button_to "Stop Bot", stop_bot_path(@bot), method: :post, class: "bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700" %>
      <%= button_to "Delete Bot", bot_path(@bot), method: :delete, class: "bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700", data: { confirm: "Are you sure you want to delete this bot?" } %>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Function to update bot status
    function updateBotStatus() {
      const statusElement = document.querySelector('.bot-status');
      const botId = statusElement.getAttribute('data-bot-id');

      fetch(`/bots/${botId}/status`)
        .then(response => response.json())
        .then(data => {
          let statusClass = 'bg-gray-600 text-gray-200';
          let statusText = 'Unknown';

          if (data.status === 'running') {
            statusClass = 'bg-green-800 text-green-200';
            statusText = 'Running';
          } else if (data.status === 'stopped') {
            statusClass = 'bg-red-800 text-red-200';
            statusText = 'Stopped';
          } else if (data.status === 'not_found') {
            statusClass = 'bg-yellow-800 text-yellow-200';
            statusText = 'Not Started';
          }

          statusElement.className = `bot-status px-2 py-1 text-sm rounded-full ${statusClass}`;
          statusElement.textContent = statusText;
        })
        .catch(error => {
          console.error('Error fetching bot status:', error);
          statusElement.className = 'bot-status px-2 py-1 text-sm rounded-full bg-red-800 text-red-200';
          statusElement.textContent = 'Error';
        });
    }

    // Update status on page load
    updateBotStatus();

    // Update status every 30 seconds
    setInterval(updateBotStatus, 30000);
  });
</script>
