<div class="container mx-auto px-4 py-8">
  <div class="bg-gray-800 rounded-lg shadow-lg p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-white">Bot Logs: <%= @bot.type %> #<%= @bot.id %></h1>
      <div class="flex space-x-2">
        <%= link_to "Refresh", logs_bot_path(@bot, lines: @lines), class: "bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700" %>
        <div class="relative">
          <button id="lines-dropdown-button" class="bg-gray-600 text-white px-3 py-2 rounded hover:bg-gray-700 flex items-center">
            <span>Lines: <%= @lines %></span>
            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
          </button>
          <div id="lines-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-gray-700 rounded-md shadow-lg z-10">
            <div class="py-1">
              <%= link_to "50 lines", logs_bot_path(@bot, lines: 50), class: "block px-4 py-2 text-sm text-gray-300 hover:bg-gray-600" %>
              <%= link_to "100 lines", logs_bot_path(@bot, lines: 100), class: "block px-4 py-2 text-sm text-gray-300 hover:bg-gray-600" %>
              <%= link_to "200 lines", logs_bot_path(@bot, lines: 200), class: "block px-4 py-2 text-sm text-gray-300 hover:bg-gray-600" %>
              <%= link_to "500 lines", logs_bot_path(@bot, lines: 500), class: "block px-4 py-2 text-sm text-gray-300 hover:bg-gray-600" %>
              <%= link_to "1000 lines", logs_bot_path(@bot, lines: 1000), class: "block px-4 py-2 text-sm text-gray-300 hover:bg-gray-600" %>
            </div>
          </div>
        </div>
      </div>
    </div>

    <% if flash[:alert] %>
      <div class="bg-red-800 text-red-200 p-4 rounded-lg mb-6">
        <%= flash[:alert] %>
      </div>
    <% end %>

    <div class="mb-6">
      <div class="flex items-center mb-2">
        <h2 class="text-xl font-semibold text-white">Status</h2>
        <% if @logs && @logs["status"] %>
          <% status_class = @logs["status"] == "running" ? "bg-green-800 text-green-200" : "bg-red-800 text-red-200" %>
          <span class="ml-2 px-2 py-1 text-xs rounded-full <%= status_class %>">
            <%= @logs["status"]&.capitalize %>
          </span>
        <% end %>
      </div>
      <% if @logs && @logs["started_at"] %>
        <p class="text-gray-400">Started: <%= Time.at(@logs["started_at"]).strftime("%Y-%m-%d %H:%M:%S") %></p>
      <% end %>
      <% if @logs && @logs["running_time_formatted"] %>
        <p class="text-gray-400">Running time: <%= @logs["running_time_formatted"] %></p>
      <% end %>
    </div>

    <div class="mb-6">
      <h2 class="text-xl font-semibold text-white mb-2">Standard Output</h2>
      <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
        <pre class="text-gray-300 whitespace-pre-wrap font-mono text-sm"><%= @logs && @logs["stdout"] ? @logs["stdout"] : "No output available" %></pre>
      </div>
    </div>

    <div class="mb-6">
      <h2 class="text-xl font-semibold text-white mb-2">Standard Error</h2>
      <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
        <pre class="text-gray-300 whitespace-pre-wrap font-mono text-sm"><%= @logs && @logs["stderr"] ? @logs["stderr"] : "No error output available" %></pre>
      </div>
    </div>

    <div class="mt-6">
      <%= link_to "Back to Bots", bots_path, class: "text-indigo-400 hover:text-indigo-300" %>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const dropdownButton = document.getElementById('lines-dropdown-button');
    const dropdown = document.getElementById('lines-dropdown');

    // Toggle dropdown
    dropdownButton.addEventListener('click', function() {
      dropdown.classList.toggle('hidden');
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
      if (!dropdownButton.contains(event.target) && !dropdown.contains(event.target)) {
        dropdown.classList.add('hidden');
      }
    });

    // Auto-refresh logs every 30 seconds
    setTimeout(function() {
      window.location.reload();
    }, 30000);
  });
</script>
