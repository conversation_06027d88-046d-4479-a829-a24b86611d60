<div class="container mx-auto px-4 py-8">
  <div class="bg-gray-800 rounded-lg shadow-lg p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-white">
        <% if @bot_type == 'Copier' %>
          Create Copier Bot
        <% else %>
          Create Trader Bot
        <% end %>
      </h1>
      <p class="text-gray-400 mt-2">
        <% if @bot_type == 'Copier' %>
          A Copier bot will copy trades from a source account to one or more destination accounts.
        <% else %>
          A trader bot will execute trades based on predefined strategies.
        <% end %>
      </p>
    </div>

    <%= form_with(model: @bot, scope: Script, url: bots_path, class: "space-y-4") do |form| %>
      <% if @bot.errors.any? %>
        <div class="bg-red-800 text-white p-4 rounded-lg mb-4">
          <h2 class="text-lg font-semibold mb-2">
            <%= pluralize(@bot.errors.count, "error") %> prohibited this bot from being saved:
          </h2>
          <ul class="list-disc list-inside">
            <% @bot.errors.full_messages.each do |message| %>
              <li><%= message %></li>
            <% end %>
          </ul>
        </div>
      <% end %>

      <%= form.hidden_field :type, value: @bot_type %>

      <% if @bot_type == 'Copier' %>
        <div>
          <%= form.label :from_account_id, "Source Account (CTrader)", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <% if @from_accounts.empty? %>
            <div class="bg-yellow-800 text-yellow-200 p-3 rounded-lg mb-2">
              <p>You don't have any CTrader accounts. Please add a CTrader account first.</p>
              <%= link_to "Add CTrader Account", new_account_path(type: 'ctrader'), class: "text-yellow-100 underline" %>
            </div>
          <% else %>
            <%= form.collection_select :from_account_id, @from_accounts, :id, :display_name,
                { prompt: "Select a CTrader account" },
                { class: "w-full bg-gray-700 border border-gray-600 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500", required: true } %>
          <% end %>
        </div>

        <div>
          <%= form.label :to_account_ids, "Destination Accounts (MT5)", class: "block text-sm font-medium text-gray-300 mb-1" %>
          <% if @to_accounts.empty? %>
            <div class="bg-yellow-800 text-yellow-200 p-3 rounded-lg mb-2">
              <p>You don't have any MT5 accounts. Please add an MT5 account first.</p>
              <%= link_to "Add MT5 Account", new_account_path(type: 'mt5'), class: "text-yellow-100 underline" %>
            </div>
          <% else %>
            <div class="bg-gray-700 border border-gray-600 rounded-md p-3">
              <% @to_accounts.each do |account| %>
                <div class="flex items-center mb-2">
                  <%= check_box_tag 'script[to_account_ids][]', account.id, false,
                      id: "script_to_account_ids_#{account.id}",
                      class: "w-4 h-4 bg-gray-700 border-gray-600 rounded focus:ring-blue-600" %>
                  <%= label_tag "script_to_account_ids_#{account.id}",
                      "#{account.name || 'MT5 Account'} (Login: #{account.accountable.login}, Server: #{account.accountable.server})",
                      class: "ml-2 text-sm text-gray-300" %>
                </div>
              <% end %>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="bg-yellow-800 text-yellow-200 p-4 rounded-lg mb-4">
          <p class="font-medium">Trader bots are not yet supported.</p>
          <p class="mt-2">This feature will be available in a future update.</p>
        </div>
      <% end %>

      <div class="flex justify-between pt-4">
        <%= link_to "Cancel", bots_path, class: "bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700" %>
        <%= form.submit "Create Bot", class: "bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700", disabled: (@from_accounts.empty? || @to_accounts.empty?) && @bot_type == 'Copier' %>
      </div>
    <% end %>
  </div>
</div>
