<!DOCTYPE html>
<html data-controller="theme" data-theme-target="html" class="dark">
  <head>
    <title>Admin Dashboard - Flamint</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>

    <script>
      // Initialize theme from localStorage on page load
      document.addEventListener('DOMContentLoaded', function() {
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
          document.documentElement.classList.add('dark');
        } else if (savedTheme === 'light') {
          document.documentElement.classList.remove('dark');
        }
      });
    </script>

    <style>
      /* Base styles for dark mode */
      .dark body {
        background-color: #111827; /* bg-gray-900 */
        color: #f9fafb; /* text-gray-50 */
      }

      /* Dark mode alert styles */
      .dark .alert-success {
        background-color: rgba(6, 78, 59, 0.5); /* bg-green-900 with opacity */
        border-color: #065f46; /* border-green-800 */
        color: #a7f3d0; /* text-green-200 */
      }

      .dark .alert-error {
        background-color: rgba(127, 29, 29, 0.5); /* bg-red-900 with opacity */
        border-color: #991b1b; /* border-red-800 */
        color: #fecaca; /* text-red-200 */
      }
    </style>
  </head>

  <body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-200">
    <nav class="bg-gray-200 dark:bg-gray-800 text-gray-900 dark:text-white shadow-md">
      <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <a href="/" class="text-xl font-bold text-gray-900 dark:text-white">Flamint Admin</a>
          </div>
          <div class="flex items-center space-x-4">
            <%= link_to "Dashboard", admin_dashboard_path, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" %>
            <%= link_to "Docker Containers", docker_containers_path, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" %>
            <a href="#" class="text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">Settings</a>
            <span class="text-gray-700 dark:text-gray-300 mr-2">Admin: <%= current_user.name || current_user.email %></span>
            <%= button_to "Logout", destroy_user_session_path, method: :delete, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white bg-transparent border-none cursor-pointer" %>

            <!-- Theme toggle button -->
            <button
              data-action="theme#toggle"
              class="p-2 rounded-md bg-gray-300 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors duration-200"
              aria-label="Toggle dark mode"
            >
              <span data-theme-target="icon">
                <!-- Icon will be set by the theme controller -->
              </span>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div class="container mx-auto px-4 py-8">
      <div id="flash_messages" data-controller="flash" data-flash-target="container">
        <% if flash[:notice] %>
          <div id="flash_message" class="bg-green-100 dark:bg-green-900/50 border border-green-400 dark:border-green-700 text-green-700 dark:text-green-200 px-4 py-3 rounded relative mb-4 alert-success" role="alert" data-controller="flash">
            <span class="block sm:inline"><%= flash[:notice] %></span>
            <button class="absolute top-0 bottom-0 right-0 px-4 py-3 text-gray-400 hover:text-gray-900 dark:hover:text-white" data-action="flash#closeImmediately">
              <svg class="h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z"/>
              </svg>
            </button>
          </div>
        <% end %>
        <% if flash[:alert] %>
          <div id="flash_message" class="bg-red-100 dark:bg-red-900/50 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded relative mb-4 alert-error" role="alert" data-controller="flash">
            <span class="block sm:inline"><%= flash[:alert] %></span>
            <button class="absolute top-0 bottom-0 right-0 px-4 py-3 text-gray-400 hover:text-gray-900 dark:hover:text-white" data-action="flash#closeImmediately">
              <svg class="h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                <path d="M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z"/>
              </svg>
            </button>
          </div>
        <% end %>
      </div>

      <%= turbo_stream_from "user_#{current_user.id}_flash" if user_signed_in? %>

      <%= yield %>
    </div>
  </body>
</html>
