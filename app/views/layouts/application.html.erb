<!DOCTYPE html>
<html data-controller="theme" data-theme-target="html">
  <head>
    <title><%= content_for(:title) || "Flamint" %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>

    <%= yield :head %>

    <%# Enable PWA manifest for installable apps (make sure to enable in config/routes.rb too!) %>
    <%#= tag.link rel: "manifest", href: pwa_manifest_path(format: :json) %>

    <link rel="icon" href="/icon.png" type="image/png">
    <link rel="icon" href="/icon.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" href="/icon.png">

    <%# Includes all stylesheet files in app/assets/stylesheets %>
    <%= stylesheet_link_tag :app, "data-turbo-track": "reload" %>
    <%= javascript_importmap_tags %>

    <script>
      // Initialize theme from localStorage on page load
      document.addEventListener('DOMContentLoaded', function() {
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
          document.documentElement.classList.add('dark');
        } else if (savedTheme === 'light') {
          document.documentElement.classList.remove('dark');
        }
      });
    </script>

    <style>
      /* Base styles for dark mode */
      .dark body {
        background-color: #111827; /* bg-gray-900 */
        color: #f9fafb; /* text-gray-50 */
      }

      /* Dark mode alert styles */
      .dark .alert-success {
        background-color: rgba(6, 78, 59, 0.5); /* bg-green-900 with opacity */
        border-color: #065f46; /* border-green-800 */
        color: #a7f3d0; /* text-green-200 */
      }

      .dark .alert-error {
        background-color: rgba(127, 29, 29, 0.5); /* bg-red-900 with opacity */
        border-color: #991b1b; /* border-red-800 */
        color: #fecaca; /* text-red-200 */
      }
    </style>
  </head>

  <body class="bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-200">
    <div class="container mx-auto px-4 py-2">
      <div class="flex justify-between items-center">
        <div>
          <%= link_to "Flamint", root_path, class: "text-gray-900 dark:text-white text-xl font-bold" %>
        </div>
        <div class="flex items-center space-x-4">
          <% if user_signed_in? %>
            <span class="text-gray-700 dark:text-gray-300">Hello, <%= current_user.name || current_user.email %></span>
            <%= link_to "Dashboard", user_dashboard_path, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" %>
            <%= link_to "Accounts", accounts_path, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" %>
            <%= link_to "Bots", bots_path, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" %>
            <%= link_to "Profile", edit_user_registration_path, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" %>
            <%= button_to "Sign out", destroy_user_session_path, method: :delete, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white bg-transparent border-none cursor-pointer" %>
          <% else %>
            <%= link_to "Sign in", new_user_session_path, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" %>
            <%= link_to "Sign up", new_user_registration_path, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" %>
          <% end %>
          <% if user_signed_in? && current_user.admin? %>
            <%= link_to "Admin Dashboard", admin_dashboard_path, class: "text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" %>
          <% end %>

          <!-- Theme toggle button -->
          <button
            data-action="theme#toggle"
            class="p-2 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
            aria-label="Toggle dark mode"
          >
            <span data-theme-target="icon">
              <!-- Icon will be set by the theme controller -->
            </span>
          </button>
        </div>
      </div>
    </div>

    <div id="flash_messages" data-controller="flash" data-flash-target="container">
      <% if notice %>
        <div id="flash_message" class="bg-green-100 dark:bg-green-900/50 border border-green-400 dark:border-green-700 text-green-700 dark:text-green-200 px-4 py-3 rounded relative mb-4 mx-4 alert-success" role="alert" data-controller="flash">
          <span class="block sm:inline"><%= notice %></span>
          <button class="absolute top-0 bottom-0 right-0 px-4 py-3 text-gray-400 hover:text-gray-900 dark:hover:text-white" data-action="flash#closeImmediately">
            <svg class="h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z"/>
            </svg>
          </button>
        </div>
      <% end %>
      <% if alert %>
        <div id="flash_message" class="bg-red-100 dark:bg-red-900/50 border border-red-400 dark:border-red-700 text-red-700 dark:text-red-200 px-4 py-3 rounded relative mb-4 mx-4 alert-error" role="alert" data-controller="flash">
          <span class="block sm:inline"><%= alert %></span>
          <button class="absolute top-0 bottom-0 right-0 px-4 py-3 text-gray-400 hover:text-gray-900 dark:hover:text-white" data-action="flash#closeImmediately">
            <svg class="h-4 w-4 fill-current" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M10 8.586L2.929 1.515 1.515 2.929 8.586 10l-7.071 7.071 1.414 1.414L10 11.414l7.071 7.071 1.414-1.414L11.414 10l7.071-7.071-1.414-1.414L10 8.586z"/>
            </svg>
          </button>
        </div>
      <% end %>
    </div>

    <%= turbo_stream_from "user_#{current_user.id}_flash" if user_signed_in? %>

    <%= yield %>
  </body>
</html>
