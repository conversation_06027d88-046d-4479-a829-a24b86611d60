<div class="bg-indigo-50 dark:bg-gray-800 p-8 rounded-lg shadow-lg border border-indigo-100 dark:border-gray-700">
  <h2 class="text-indigo-900 dark:text-white text-xl font-semibold mb-8">Join over 3,000 creators</h2>

  <div class="space-y-3">
    <!-- Social login buttons -->
    <div class="grid grid-cols-2 gap-3">
      <a href="#" class="flex justify-center items-center py-2 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition">
        Google
      </a>
      <a href="#" class="flex justify-center items-center py-2 px-4 bg-pink-500 text-white rounded-md hover:bg-pink-600 transition">
        Dribbble
      </a>
    </div>

    <!-- Divider -->
    <div class="flex items-center justify-center text-gray-500 dark:text-gray-400 text-sm my-3">
      <span>or</span>
    </div>

    <!-- Email form -->
    <% resource = local_assigns[:resource] || User.new %>
    <% resource_name = local_assigns[:resource_name] || :user %>
    <%= form_for(resource, as: resource_name, url: registration_path(:user), html: { class: "space-y-3" }) do |f| %>
      <% if defined?(Devise) %>
        <%= render "devise/shared/error_messages", resource: resource %>
      <% end %>

      <div class="mb-5">
        <%= f.label :name, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <div class="relative">
          <%= f.text_field :name, autofocus: true, class: "w-full p-2.5 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white rounded-lg focus:ring-blue-500 focus:border-blue-500" %>
        </div>
      </div>

      <div class="mb-5">
        <%= f.label :email, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <div class="relative">
          <%= f.email_field :email, placeholder: "<EMAIL>", class: "w-full p-2.5 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white rounded-lg focus:ring-blue-500 focus:border-blue-500" %>
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
              <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
            </svg>
          </div>
        </div>
      </div>

      <div class="mb-5">
        <%= f.label :password, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <div class="relative">
          <%= f.password_field :password, autocomplete: "new-password", class: "w-full p-2.5 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white rounded-lg focus:ring-blue-500 focus:border-blue-500" %>
          <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
            <svg class="w-5 h-5 text-gray-500 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
            </svg>
          </div>
        </div>
        <% if @minimum_password_length %>
          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400"><%= @minimum_password_length %> characters minimum</p>
        <% end %>
      </div>

      <div class="mb-5">
        <%= f.label :password_confirmation, class: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2" %>
        <div class="relative">
          <%= f.password_field :password_confirmation, autocomplete: "new-password", class: "w-full p-2.5 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-white rounded-lg focus:ring-blue-500 focus:border-blue-500" %>
        </div>
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <%= f.check_box :remember_me, class: "w-4 h-4 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-blue-600" %>
          <%= f.label :remember_me, class: "ml-2 text-sm text-gray-700 dark:text-gray-300" %>
        </div>
        <%= link_to "Lost Password?", new_password_path(:user), class: "text-sm text-pink-600 dark:text-pink-500 hover:underline" %>
      </div>

      <%= f.submit "Create an account", class: "w-full text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-4 focus:ring-indigo-300 dark:focus:ring-indigo-800 font-medium rounded-lg text-sm px-5 py-2.5 text-center" %>
    <% end %>

    <div class="text-sm text-gray-600 dark:text-gray-400 text-center mt-3">
      Already registered? <%= link_to "Sign in", new_session_path(:user), class: "text-indigo-600 dark:text-indigo-400 hover:underline" %>
    </div>
  </div>
</div>
