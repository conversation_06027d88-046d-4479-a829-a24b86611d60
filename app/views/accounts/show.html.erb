<div class="container mx-auto px-4 py-8">
  <div class="bg-gray-800 rounded-lg shadow-lg p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-white">
        <%= @account.name || (@account.accountable_type == 'Mt5Account' ? 'MT5 Account' : 'CTrader Account') %>
      </h1>
      <p class="text-gray-400">
        <%= @account.accountable_type == 'Mt5Account' ? 'MetaTrader 5' : 'cTrader' %> Account Details
      </p>
    </div>

    <div class="bg-gray-700 rounded-lg p-6 mb-6">
      <% if @account.accountable_type == 'Mt5Account' %>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 class="text-sm font-medium text-gray-400">Login</h3>
            <p class="text-white"><%= @account.accountable.login %></p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-400">Server</h3>
            <p class="text-white"><%= @account.accountable.server %></p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-400">Password</h3>
            <p class="text-white">••••••••</p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-400">Created</h3>
            <p class="text-white"><%= @account.created_at.strftime("%B %d, %Y") %></p>
          </div>
        </div>
      <% else %>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 class="text-sm font-medium text-gray-400">Account Number</h3>
            <p class="text-white"><%= @account.accountable.account_number %></p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-400">Account ID</h3>
            <p class="text-white"><%= @account.accountable.account_id %></p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-400">Host Type</h3>
            <p class="text-white"><%= @account.accountable.host_type.capitalize %></p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-400">Token Status</h3>
            <p class="text-white">
              <% if @account.accountable.access_token.present? %>
                <% if @account.accountable.token_expired? %>
                  <span class="px-2 py-1 text-xs rounded-full bg-yellow-800 text-yellow-200">Expired</span>
                <% else %>
                  <span class="px-2 py-1 text-xs rounded-full bg-green-800 text-green-200">Active</span>
                <% end %>
              <% else %>
                <span class="px-2 py-1 text-xs rounded-full bg-red-800 text-red-200">Missing</span>
              <% end %>
            </p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-400">Token Expiry</h3>
            <p class="text-white">
              <% if @account.accountable.access_token.present? && @account.accountable.updated_at.present? && @account.accountable.expires_in.present? %>
                <%= (@account.accountable.updated_at + @account.accountable.expires_in.seconds).strftime("%B %d, %Y %H:%M") %>
              <% else %>
                N/A
              <% end %>
            </p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-400">Auto Refresh Token</h3>
            <p class="text-white">
              <% if @account.accountable.auto_refresh_token %>
                <span class="px-2 py-1 text-xs rounded-full bg-green-800 text-green-200">Enabled</span>
              <% else %>
                <span class="px-2 py-1 text-xs rounded-full bg-red-800 text-red-200">Disabled</span>
              <% end %>
            </p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-400">Created</h3>
            <p class="text-white"><%= @account.created_at.strftime("%B %d, %Y") %></p>
          </div>
        </div>
      <% end %>
    </div>

    <div class="flex space-x-4">
      <%= link_to "Back to Accounts", accounts_path, class: "bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700" %>
      <%= link_to "Edit Account", edit_account_path(@account), class: "bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700" %>
      <% if @account.accountable_type == 'CtraderAccount' %>
        <%= link_to "Get Access Token", get_ctrader_token_account_path(@account), class: "bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700" %>
        <%= button_to "Refresh Token", refresh_ctrader_token_account_path(@account), method: :post, class: "bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700", remote: true %>
      <% end %>
      <%= button_to "Delete Account", account_path(@account), method: :delete, class: "bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700", data: { confirm: "Are you sure you want to delete this account?" } %>
    </div>
  </div>
</div>
