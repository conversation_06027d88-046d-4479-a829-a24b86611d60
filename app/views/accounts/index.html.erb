<div class="container mx-auto px-4 py-8">
  <div class="bg-gray-800 rounded-lg shadow-lg p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-white">Your Trading Accounts</h1>
      <div class="flex space-x-2">
        <%= link_to "Add MT5 Account", new_account_path(type: 'mt5'), class: "bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700" %>
        <%= link_to "Add CTrader Account", new_account_path(type: 'ctrader'), class: "bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700" %>
      </div>
    </div>

    <% if @accounts.empty? %>
      <div class="bg-gray-700 p-6 rounded-lg text-center">
        <p class="text-gray-300 mb-4">You don't have any trading accounts yet.</p>
        <p class="text-gray-400">Add your first account to get started with trading bots.</p>
      </div>
    <% else %>
      <div class="mb-6">
        <h2 class="text-xl font-semibold text-white mb-4">MT5 Accounts</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-gray-700 rounded-lg overflow-hidden">
            <thead class="bg-gray-600">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Name</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Login</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Server</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-600">
              <% @accounts.mt5.each do |account| %>
                <tr>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300"><%= account.name || "MT5 Account" %></td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300"><%= account.accountable.login %></td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300"><%= account.accountable.server %></td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                    <div class="flex space-x-2">
                      <%= link_to "Details", account_path(account), class: "text-indigo-400 hover:text-indigo-300" %>
                      <%= link_to "Edit", edit_account_path(account), class: "text-blue-400 hover:text-blue-300" %>
                      <%= button_to "Delete", account_path(account), method: :delete, class: "text-red-400 hover:text-red-300 bg-transparent border-none cursor-pointer", data: { confirm: "Are you sure you want to delete this account?" } %>
                    </div>
                  </td>
                </tr>
              <% end %>
              <% if @accounts.mt5.empty? %>
                <tr>
                  <td colspan="4" class="px-4 py-3 text-center text-sm text-gray-400">No MT5 accounts found</td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h2 class="text-xl font-semibold text-white mb-4">CTrader Accounts</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-gray-700 rounded-lg overflow-hidden">
            <thead class="bg-gray-600">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Name</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Account Number</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Host Type</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Token Status</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-600">
              <% @accounts.ctrader.each do |account| %>
                <tr>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300"><%= account.name || "CTrader Account" %></td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300"><%= account.accountable.account_number %></td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300"><%= account.accountable.host_type %></td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                    <% if account.accountable.access_token.present? %>
                      <% if account.accountable.token_expired? %>
                        <span class="px-2 py-1 text-xs rounded-full bg-yellow-800 text-yellow-200">Expired</span>
                      <% else %>
                        <span class="px-2 py-1 text-xs rounded-full bg-green-800 text-green-200">Active</span>
                      <% end %>
                    <% else %>
                      <span class="px-2 py-1 text-xs rounded-full bg-red-800 text-red-200">Missing</span>
                    <% end %>
                  </td>
                  <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-300">
                    <div class="flex space-x-2">
                      <%= link_to "Details", account_path(account), class: "text-indigo-400 hover:text-indigo-300" %>
                      <%= link_to "Edit", edit_account_path(account), class: "text-blue-400 hover:text-blue-300" %>
                      <%= link_to "Get Token", get_ctrader_token_account_path(account), class: "text-green-400 hover:text-green-300", target: :_blank %>
                      <%= button_to "Refresh Token", refresh_ctrader_token_account_path(account), method: :post, class: "text-yellow-400 hover:text-yellow-300 bg-transparent border-none cursor-pointer", remote: true %>
                      <%= button_to "Delete", account_path(account), method: :delete, class: "text-red-400 hover:text-red-300 bg-transparent border-none cursor-pointer", data: { confirm: "Are you sure you want to delete this account?" } %>
                    </div>
                  </td>
                </tr>
              <% end %>
              <% if @accounts.ctrader.empty? %>
                <tr>
                  <td colspan="5" class="px-4 py-3 text-center text-sm text-gray-400">No CTrader accounts found</td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>
    <% end %>

    <div class="mt-6">
      <%= link_to "Back to Dashboard", user_dashboard_path, class: "text-indigo-400 hover:text-indigo-300" %>
    </div>
  </div>
</div>
