import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["container"]

  connect() {
    // Auto-hide flash messages after 5 seconds if they're not error messages
    if (this.element.classList.contains('alert-success')) {
      setTimeout(() => {
        this.fadeOut()
      }, 5000)
    }
  }

  // Method to fade out and remove a flash message
  fadeOut(element = this.element) {
    // Add fade-out animation
    element.style.transition = 'opacity 0.5s ease'
    element.style.opacity = '0'

    // Remove the element after animation completes
    setTimeout(() => {
      if (element && element.parentNode) {
        element.parentNode.removeChild(element);
      }
    }, 500)
  }

  // Method to close a flash message immediately without animation
  closeImmediately() {
    // Use this.element when called from HTML with data-action
    const elementToRemove = this.element;

    if (elementToRemove && elementToRemove.parentNode) {
      elementToRemove.parentNode.removeChild(elementToRemove);
    }
  }

  // Method to clear all flash messages
  clearAll() {
    Array.from(this.containerTarget.children).forEach(child => {
      this.fadeOut(child)
    })
  }
}
