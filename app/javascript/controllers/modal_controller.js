import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    // Initialize modal functionality
    this.setupDestroyModal();
  }

  setupDestroyModal() {
    const destroyBtn = document.getElementById('destroy-all-btn');
    const modal = document.getElementById('destroy-modal');
    const cancelBtn = document.getElementById('cancel-destroy');

    if (destroyBtn && modal && cancelBtn) {
      // Show modal when destroy button is clicked
      destroyBtn.addEventListener('click', () => {
        modal.classList.remove('hidden');
      });

      // Hide modal when cancel button is clicked
      cancelBtn.addEventListener('click', () => {
        modal.classList.add('hidden');
      });

      // Hide modal when clicking outside the modal content
      modal.addEventListener('click', (event) => {
        if (event.target === modal) {
          modal.classList.add('hidden');
        }
      });
    }
  }
}
