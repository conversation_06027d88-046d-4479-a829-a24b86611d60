//@version=5
strategy("BTCUSD Breakout/Reversion Daily Candle Strategy", overlay=true, initial_capital=10000, default_qty_type=strategy.fixed, default_qty_value=1, calc_on_every_tick=true)

// Input parameters
tpSlDistance = input.int(400, title="TP/SL Distance (points)", minval=100, maxval=2000, step=100)
maxDailyLosses = input.int(2, title="Max Daily Losses", minval=1, maxval=10)

// Variables to track daily losses
var dailyLosses = 0
var float bodyHigh = na
var float bodyLow = na
var bool longFilled = false
var bool shortFilled = false
var bool disableOrdersForDay = false

// Reset variables on new day
if (timeframe.change("D"))
    dailyLosses := 0
    longFilled := false
    shortFilled := false
    disableOrdersForDay := false

// Initialize previous candle body high and low
if barstate.isfirst
    bodyHigh := na
    bodyLow := na

// Calculate body high and low after previous candle completes
if barstate.isconfirmed and not na(close[1])
    bodyHigh := math.max(open[1], close[1])
    bodyLow := math.min(open[1], close[1])

// Determine if current price is inside the previous candle's body
isInsideBody = (close > bodyLow) and (close < bodyHigh)

// Determine order type based on price position
orderType = isInsideBody ? "stop" : "limit"

// Define buy and sell order prices
buyOrderPrice = bodyHigh
sellOrderPrice = bodyLow

// Track entries for visual and alert purposes
var float longEntryPrice = na
var float shortEntryPrice = na

// Handle order execution and management
if not na(bodyHigh) and not na(bodyLow) and strategy.position_size == 0 and not disableOrdersForDay and barstate.isconfirmed
    // Clear any existing orders
    strategy.cancel_all()

    // Place the appropriate orders based on the order type
    if orderType == "stop"
        // Stop orders - for when price is inside the body
        strategy.entry("Long Entry", strategy.long, stop=buyOrderPrice)
        strategy.entry("Short Entry", strategy.short, stop=sellOrderPrice)
    else
        // Limit orders - for when price is outside the body
        strategy.entry("Long Entry", strategy.long, limit=buyOrderPrice)
        strategy.entry("Short Entry", strategy.short, limit=sellOrderPrice)

    // Save entry prices for plotting
    longEntryPrice := buyOrderPrice
    shortEntryPrice := sellOrderPrice

// Set up TP/SL based on entry
if strategy.position_size > 0 and not longFilled
    // Long position
    strategy.exit("Long TP/SL", "Long Entry", profit=tpSlDistance, loss=tpSlDistance)
    strategy.cancel("Short Entry")
    longFilled := true
    shortFilled := false

if strategy.position_size < 0 and not shortFilled
    // Short position
    strategy.exit("Short TP/SL", "Short Entry", profit=tpSlDistance, loss=tpSlDistance)
    strategy.cancel("Long Entry")
    shortFilled := true
    longFilled := false

// Track daily losses
if strategy.closedtrades > 0
    lastTrade = strategy.closedtrades - 1
    if strategy.closedtrades.profit(lastTrade) < 0
        dailyLosses := dailyLosses + 1
        if dailyLosses >= maxDailyLosses
            disableOrdersForDay := true
            strategy.cancel_all()

// Plot entry levels and TP/SL
plot(longEntryPrice, color=color.green, style=plot.style_circles, linewidth=2, title="Buy Order")
plot(shortEntryPrice, color=color.red, style=plot.style_circles, linewidth=2, title="Sell Order")

// Create alert conditions
alertcondition(strategy.position_size[1] == 0 and strategy.position_size != 0, title="Order Filled Alert", message="Order has been filled!")

// Display strategy info label
var label infoLabel = na
label.delete(infoLabel)
// Create label text
var labelText = "Order Type: " + orderType + "\nBuy @ " + str.tostring(buyOrderPrice) + "\nSell @ " + str.tostring(sellOrderPrice) + "\nDaily Losses: " + str.tostring(dailyLosses) + "/" + str.tostring(maxDailyLosses)
// Create label in a single line
infoLabel := label.new(bar_index, high + (high * 0.01), text=labelText, color=color.new(color.blue, 80), textcolor=color.white, style=label.style_label_down)

// For backtest reporting
if barstate.islastconfirmedhistory
    strategy.close_all()
