# README

Command to run mt5 server search to add IP server to MT5:
docker exec -u 911:911 mt5 wine cmd /c "python /scripts/mt5serversearch.py <server-name>"


Command to localhost dev bride network:
docker run -d --name web \
  --network kamal \
  --restart unless-stopped \
  -p 3001:3001 \
  --add-host=host.docker.internal:host-gateway \
  alpine:latest \
  sh -c "apk add --no-cache socat && socat TCP-LISTEN:3001,fork TCP:host.docker.internal:3001"


This README would normally document whatever steps are necessary to get the
application up and running.

Things you may want to cover:

* Ruby version

* Configuration

* Database creation

* Database initialization

* How to run the test suite

* Services (job queues, cache servers, search engines, etc.)

* Deployment instructions

* ...
