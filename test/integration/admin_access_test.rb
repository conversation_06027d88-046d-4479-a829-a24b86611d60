require "test_helper"
require "database_cleaner/active_record"

class AdminAccessTest < ActionDispatch::IntegrationTest
  setup do
    DatabaseCleaner.strategy = :transaction
    DatabaseCleaner.start
    
    # Create a regular user
    @user = User.create!(
      name: "Regular User",
      email: "<EMAIL>",
      password: "password123",
      password_confirmation: "password123",
      admin: false
    )
    
    # Create an admin user
    @admin = User.create!(
      name: "Admin User",
      email: "<EMAIL>",
      password: "password123",
      password_confirmation: "password123",
      admin: true
    )
  end
  
  teardown do
    DatabaseCleaner.clean
  end
  
  test "regular user cannot access admin dashboard" do
    # Sign in as regular user
    post user_session_path, params: { 
      user: { 
        email: @user.email, 
        password: "password123" 
      } 
    }
    
    # Try to access admin dashboard
    get admin_dashboard_path
    
    # Should be redirected with an alert
    assert_redirected_to root_path
    follow_redirect!
    assert_select "div.bg-red-100", /You must be an admin to access this section/
  end
  
  test "admin user can access admin dashboard" do
    # Sign in as admin user
    post user_session_path, params: { 
      user: { 
        email: @admin.email, 
        password: "password123" 
      } 
    }
    
    # Try to access admin dashboard
    get admin_dashboard_path
    
    # Should be successful
    assert_response :success
    assert_select "h1", "Admin Dashboard"
  end
  
  test "unauthenticated user cannot access admin dashboard" do
    # Try to access admin dashboard without signing in
    get admin_dashboard_path
    
    # Should be redirected to sign in page
    assert_redirected_to new_user_session_path
  end
end
