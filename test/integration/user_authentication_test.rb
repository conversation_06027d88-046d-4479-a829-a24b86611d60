require "test_helper"
require "database_cleaner/active_record"

class UserAuthenticationTest < ActionDispatch::IntegrationTest
  setup do
    DatabaseCleaner.strategy = :transaction
    DatabaseCleaner.start
  end

  teardown do
    DatabaseCleaner.clean
  end
  test "user can sign up" do
    get new_user_registration_path
    assert_response :success

    assert_difference('User.count') do
      post user_registration_path, params: {
        user: {
          name: "Test User",
          email: "<EMAIL>",
          password: "password123",
          password_confirmation: "password123"
        }
      }
    end

    follow_redirect!
    assert_response :success
    assert_select "span", /Hello, Test User/
  end

  test "user can sign in" do
    user = User.create!(
      name: "Test User",
      email: "<EMAIL>",
      password: "password123",
      password_confirmation: "password123"
    )

    get new_user_session_path
    assert_response :success

    post user_session_path, params: {
      user: {
        email: user.email,
        password: "password123"
      }
    }

    follow_redirect!
    assert_response :success
    assert_select "span", /Hello, Test User/
  end

  test "user can access dashboard when signed in" do
    user = User.create!(
      name: "Test User",
      email: "<EMAIL>",
      password: "password123",
      password_confirmation: "password123"
    )

    post user_session_path, params: {
      user: {
        email: user.email,
        password: "password123"
      }
    }

    get user_dashboard_path
    assert_response :success
    assert_select "h1", /Welcome to your Dashboard/
  end

  test "user cannot access dashboard when not signed in" do
    get user_dashboard_path
    assert_redirected_to new_user_session_path
  end
end
