# This file should ensure the existence of records required to run the application in every environment (production,
# development, test). The code here should be idempotent so that it can be executed at any point in every environment.
# The data can then be loaded with the bin/rails db:seed command (or created alongside the database with db:setup).

# Create admin user
admin_email = '<EMAIL>'

# Only create if admin doesn't exist
unless User.exists?(email: admin_email)
  admin = User.create!(
    name: 'Flamint',
    email: admin_email,
    password: ENV['ADMIN_PASSWORD'],
    password_confirmation: ENV['ADMIN_PASSWORD'],
    admin: true
  )

  puts "Admin user created with email: #{admin.email}"
end

user = User.find_by(email: admin_email)
# Create CTrader accounts
if ENV['CTRADER_TEST_ACCOUNT_ID'].present?

  # Check if account already exists
  unless CtraderAccount.exists?(account_id: ENV['CTRADER_TEST_ACCOUNT_ID'])
    ctrader_account = CtraderAccount.create!(
      account_id: ENV['CTRADER_TEST_ACCOUNT_ID'],
      account_number: ENV['CTRADER_TEST_ACCOUNT_NUMBER'],
      host_type: 'demo',
      access_token: ENV['CTRADER_TEST_ACCESS_TOKEN'],
      refresh_token: ENV['CTRADER_TEST_REFRESH_TOKEN'],
      expires_in: 2628000,
      updated_at: ENV['CTRADER_TEST_ACCESS_TOKEN_UPDATED_AT']
    )

    # Create account association
    Account.create!(
      user: user,
      accountable: ctrader_account
    )

    puts "CTrader account created with ID: #{ctrader_account.account_id}"
  else
    puts "CTrader account with ID #{ENV['CTRADER_ACCOUNT_ID']} already exists"
  end
end

# Create MT5 accounts
if ENV['MT5_TEST1_LOGIN'].present?

  # Check if account already exists
  unless Mt5Account.exists?(login: ENV['MT5_TEST1_LOGIN'])
    mt5_account = Mt5Account.create!(
      login: ENV['MT5_TEST1_LOGIN'],
      password: ENV['MT5_TEST1_PASSWORD'],
      server: ENV['MT5_TEST1_SERVER']
    )

    # Create account association
    Account.create!(
      user: user,
      accountable: mt5_account
    )

    puts "MT5 account created with login: #{mt5_account.login}"
  else
    puts "MT5 account with login #{ENV['MT5_LOGIN']} already exists"
  end
end

if ENV['MT5_TEST2_LOGIN'].present?

  # Check if account already exists
  unless Mt5Account.exists?(login: ENV['MT5_TEST2_LOGIN'])
    mt5_account = Mt5Account.create!(
      login: ENV['MT5_TEST2_LOGIN'],
      password: ENV['MT5_TEST2_PASSWORD'],
      server: ENV['MT5_TEST2_SERVER']
    )

    # Create account association
    Account.create!(
      user: user,
      accountable: mt5_account
    )

    puts "MT5 account created with login: #{mt5_account.login}"
  else
    puts "MT5 account with login #{ENV['MT5_LOGIN']} already exists"
  end
end
