class CreateDeals < ActiveRecord::Migration[8.0]
  def change
    create_table :deals do |t|
      t.bigint :order_id
      t.bigint :position_id
      t.string :accountable_type
      t.bigint :accountable_id
      t.string :symbol
      t.float :volume
      t.float :lot
      t.integer :trade_side
      t.integer :status
      t.references :script, null: false, foreign_key: true

      t.timestamps
    end
    add_index :deals, [:order_id, :accountable_type, :accountable_id]
    add_index :deals, [:position_id, :accountable_type, :accountable_id]
  end
end
