class CreateSlaveDeals < ActiveRecord::Migration[8.0]
  def change
    create_table :slave_deals do |t|
      # Same columns as deals table
      t.bigint :order_id
      t.bigint :position_id
      t.string :accountable_type
      t.bigint :accountable_id
      t.string :symbol
      t.float :volume
      t.float :lot
      t.integer :trade_side
      t.integer :status
      t.references :script, null: false, foreign_key: true
      
      # Additional columns
      t.references :host_deal, foreign_key: { to_table: :deals }
      t.text :pending_changes

      t.timestamps
    end
    add_index :slave_deals, [:order_id, :accountable_type, :accountable_id]
    add_index :slave_deals, [:position_id, :accountable_type, :accountable_id]
  end
end
