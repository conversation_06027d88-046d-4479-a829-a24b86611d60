production:
  auto_refresh_ctrader_tokens:
    class: AutoRefreshTokenJob
    queue: default
    schedule: at 5am every day

development:
  auto_refresh_ctrader_tokens:
    class: AutoRefreshTokenJob
    queue: default
    schedule: at 5am every day

# Example configurations:
# production:
#   periodic_cleanup:
#     class: CleanSoftDeletedRecordsJob
#     queue: background
#     args: [ 1000, { batch_size: 500 } ]
#     schedule: every hour
#   periodic_command:
#     command: "SoftDeletedRecord.due.delete_all"
#     priority: 2
#     schedule: at 5am every day
