#!/bin/sh

# A sample post-deploy hook
#
# These environment variables are available:
# KAMAL_RECORDED_AT
# KAMAL_PERFORMER
# KAMAL_VERSION
# KAMAL_HOSTS
# KAMAL_ROLE (if set)
# KAMAL_DESTINATION (if set)
# <PERSON><PERSON>AL_RUNTIME



echo "$KAMAL_PERFORMER deployed $KAMAL_VERSION to $KAMAL_DESTINATION in $KAMAL_RUNTIME seconds"

# Get the container ID of the running app container
APP_CONTAINER=$(docker ps --filter "name=flamint-web-$KAMAL_VERSION" --format "{{.ID}}" | head -n 1)

# Copy scripts from the app container to the scripts volume
if [ -n "$APP_CONTAINER" ]; then
  # Create a temporary container that mounts the scripts volume
  docker exec --user root $APP_CONTAINER sh -c "cp -rf /rails/scripts/* /rails/external_scripts/"
  echo "Scripts updated successfully from /rails/scripts to scripts volume"
else
  echo "Error: Could not find the flamint-web-$KAMAL_VERSION container"
fi

