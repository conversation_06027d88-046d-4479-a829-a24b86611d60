#!/usr/bin/env python3
import pyautogui
import time
import sys

# Get command-line arguments
args = sys.argv[1:]

# Print the arguments
if __name__ == '__main__':
    pyautogui.press('esc')
    pyautogui.press('esc')
    pyautogui.hotkey('alt', 'f', 'a')

    pyautogui.press('tab')
    pyautogui.press('tab')
    pyautogui.press('tab')
    for arg in args:
        pyautogui.write(arg)
        pyautogui.press('enter')
        time.sleep(5)
    pyautogui.press('esc')
pyautogui.press('esc')
