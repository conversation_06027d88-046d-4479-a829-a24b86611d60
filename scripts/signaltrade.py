#!/usr/bin/env python
# coding: utf-8

from dotenv import dotenv_values
import os

config = {
    **dotenv_values("./scripts/dev.env"),
    **dotenv_values("dev.env"),
    **os.environ,  # override loaded values with environment variables
    **dotenv_values("custom.env"),
    **dotenv_values("./scripts/custom.env"),
}
from ctrader_open_api import Client, Protobuf, TcpProtocol, Auth, EndPoints
from ctrader_open_api.messages.OpenApiCommonMessages_pb2 import *
from ctrader_open_api.messages.OpenApiMessages_pb2 import *
from ctrader_open_api.messages.OpenApiModelMessages_pb2 import *
from twisted.internet import reactor, defer, task
import json, pickle, pytz
from db_driver import *
from datetime import datetime, timedelta, timezone
import calendar
import time
import pandas as pd
import numpy as np

db_fail_check = 0
symbols = None
account_id = int(config['ACCOUNT_ID'])
connection_pool = ConnectionPool(connection_string=config['CONNECTION_STRING'])
host = EndPoints.PROTOBUF_LIVE_HOST if config['HOST_TYPE'].lower() == "live" else EndPoints.PROTOBUF_DEMO_HOST
client = Client(host, EndPoints.PROTOBUF_PORT, TcpProtocol)
df = {}
timeframes = [15, 60, 240]
check_symbols = {
    'EURUSD': 1,
    'XAUUSD': 41,
    'NZDUSD': 12,
}
for name, id in check_symbols.items():
    df[name] = {}

class SymbolCache:
    def __init__(self):
        self.cache = {}

    def get_symbol_data_sync(self, symbol):
        if symbol.symbolName in self.cache:
            d = defer.Deferred()
            d.callback(ProtoOASymbol.FromString(self.cache[symbol.symbolName]))
            return d
        else:
            return self.fetch_symbol_data_async(symbol)

    def fetch_symbol_callback(self, result_fetch, symbol):
        result_fetch = Protobuf.extract(result_fetch)
        symbol_data = result_fetch.symbol[0]
        self.cache[symbol.symbolName] = symbol_data.SerializeToString()
        # save_detail_symbols_cache.delay(pickle.dumps(self.cache))
        return symbol_data

    def update_symbol_cache(self, symbol_ids):
        global symbols
        for symbol_id in symbol_ids:
            symbol = find_symbol_by_id(symbols, symbol_id)
            if symbol is not None:
                self.fetch_symbol_data_async(symbol)


    def fetch_symbol_data_async(self, symbol):
        request = ProtoOASymbolByIdReq()
        request.ctidTraderAccountId = account_id
        request.symbolId.extend([symbol.symbolId])
        deferred = client.send(request)
        return deferred.addCallback(lambda result: self.fetch_symbol_callback(result, symbol))

cache = SymbolCache()

host_info = {
    'account_id': config['ACCOUNT_ID'],
    'ota': 'cTrader'
}

mapped_timeframes = {
    15: ProtoOATrendbarPeriod.M15,
    60: ProtoOATrendbarPeriod.H1,
    240: ProtoOATrendbarPeriod.H4,
}

# Save access token to db if not exists
def init_access_token_db():
    if connection_pool.get_access_token(account_id).empty:
        access_token = {
            'account_id': account_id,
            'access_token': config['ACCESS_TOKEN'],
            'refresh_token': config['REFRESH_TOKEN'],
            'expires_in': config['EXPIRES_IN'],
            'updated_at': config.get('AT_UPDATED_AT')
        }
        connection_pool.insert_access_token(access_token)

def refreshAccountToken():
    request = ProtoOARefreshTokenReq()
    request.refreshToken = config['REFRESH_TOKEN']
    deferred = client.send(request)
    deferred.addCallback(refreshAccountTokenCallback)

def get_bar_trend_data(symbol_id, symbol_name, timeframe):
    deferred = get_recent_bar_ctrader(symbol_id, mapped_timeframes[timeframe], int(config['RECENT_WEEKS']))
    deferred.addCallback(lambda message: handle_result(message, symbol_name, timeframe))

def handle_result(message, symbol_name, timeframe):
    trendbars = Protobuf.extract(message)
    bars = list(map(transformTrendbar, trendbars.trendbar))
    barsData = []
    barsData.extend(bars)
    global df
    df[symbol_name][timeframe] = pd.DataFrame(np.array(barsData),
                    columns=['Time', 'Open', 'High', 'Low', 'Close', 'Volume'])
    df[symbol_name][timeframe]["Open"] = pd.to_numeric(df[symbol_name][timeframe]["Open"])
    df[symbol_name][timeframe]["High"] = pd.to_numeric(df[symbol_name][timeframe]["High"])
    df[symbol_name][timeframe]["Low"] = pd.to_numeric(df[symbol_name][timeframe]["Low"])
    df[symbol_name][timeframe]["Close"] = pd.to_numeric(df[symbol_name][timeframe]["Close"])
    df[symbol_name][timeframe]["Volume"] = pd.to_numeric(df[symbol_name][timeframe]["Volume"])
    df[symbol_name][timeframe]["Labels"] = (df[symbol_name][timeframe]["Close"] > df[symbol_name][timeframe]["Open"]).astype(int)
    df[symbol_name][timeframe].to_csv(f"{symbol_name}_{timeframe}.csv", index=False)


def transformTrendbar(trendbar):
    openTime = datetime.fromtimestamp(trendbar.utcTimestampInMinutes * 60, timezone.utc)
    openPrice = (trendbar.low + trendbar.deltaOpen) / 100000.0
    highPrice = (trendbar.low + trendbar.deltaHigh) / 100000.0
    lowPrice = trendbar.low / 100000.0
    closePrice = (trendbar.low + trendbar.deltaClose) / 100000.0
    return [openTime, openPrice, highPrice, lowPrice, closePrice, trendbar.volume]

def refreshAccountTokenCallback(message):
    message = Protobuf.extract(message)
    if message.errorCode == 'CH_ACCESS_TOKEN_INVALID':
        print('Refresh token invalid')
        reactor.stop()
        return
    print('Get new token access: ', message.accessToken)

    access_token = {
        'account_id': account_id,
        'access_token': message.accessToken,
        'refresh_token': message.accessToken,
        'expires_in': message.expiresIn
    }
    connection_pool.refresh_access_token(access_token)

    d = defer.Deferred()
    d.addCallback(accountAuthResponseCallback)

def find_symbol_by_id(symbols, target_id):
    if symbols is None:
        return None
    for symbol in symbols:
        if symbol.symbolId == target_id:
            return symbol

def symbolsResponseCallback(result):
    print("\nSymbols received")
    global timeframes, symbols

    try:
        symbols = Protobuf.extract(result).symbol
    except Exception:
        print('Get symbols data error, invalid access token or request')
        return

    for symbol_name, symbol_id in check_symbols.items():
        if symbol_id is None:
            for symbol in symbols:
                if symbol.symbolName == symbol_name:
                    check_symbols[symbol_name] = symbol.symbolId
                    symbol_id = symbol.symbolId
                    break
        for timeframe in timeframes:
            get_bar_trend_data(symbol_id, symbol_name, timeframe)

def accountAuthResponseCallback(result):
    print("\nAccount authenticated")
    request = ProtoOASymbolsListReq()
    request.ctidTraderAccountId = account_id
    request.includeArchivedSymbols = False
    deferred = client.send(request)
    deferred.addCallbacks(symbolsResponseCallback, onError)

def applicationAuthResponseCallback(result):
    print("\nApplication authenticated")
    print("\nAccount ID: ", account_id)
    print("\nHost Type: ", config['HOST_TYPE'])

    df_access_token = connection_pool.get_access_token(account_id)
    if df_access_token is None:
        print('Can not find access token for this account: ', account_id)
        reactor.stop()
        return
    # Token expired?
    current_timezone = pytz.timezone(config['TZ'])
    expired_time = df_access_token.updated_at[0].to_pydatetime().astimezone(current_timezone) + \
        timedelta(seconds=int(df_access_token.expires_in[0]))
    if datetime.now(current_timezone) > expired_time:
        refreshAccountToken()
    else:
        request = ProtoOAAccountAuthReq()
        request.ctidTraderAccountId = account_id
        request.accessToken = df_access_token.access_token[0]
        deferred = client.send(request)
        deferred.addCallbacks(accountAuthResponseCallback, onError)

def onError(message): # Call back for errors
    print("\nMessage Error Callback = ", Protobuf.extract(message))

def disconnected(client, reason): # Callback for client disconnection
    print("\nDisconnected = ", reason)

def onMessageReceived(client, message): # Callback for receiving all messages
    if message.payloadType in [ProtoHeartbeatEvent().payloadType, ProtoOAAccountAuthRes().payloadType,\
    ProtoOAApplicationAuthRes().payloadType, ProtoOASymbolsListRes().payloadType, ProtoOAGetTrendbarsRes().payloadType, ProtoOASymbolByIdRes().payloadType]:
        return

    extracted_message = Protobuf.extract(message)
    print("\nMessage type = \n", message.payloadType)
    print("\nMessage received = \n", extracted_message)

    global db_fail_check
    if db_fail_check > 0:
        e = connection_pool.check_health()
        if e != True:
            print("DB not good health now, ignore message event")
            return
        else:
            db_fail_check = 0

    if message.payloadType == ProtoOAErrorRes().payloadType:
        if extracted_message.errorCode == 'CH_ACCESS_TOKEN_INVALID':
            refreshAccountToken()

    if message.payloadType == ProtoOASymbolChangedEvent().payloadType:
        cache.update_symbol_cache(extracted_message.symbolId)
        return

def connected(client): # Callback for client connection
    print("\nConnected")
    loop_health_db_check = task.LoopingCall(db_check_health)
    loop_health_db_check.start(120)
    request = ProtoOAApplicationAuthReq()
    request.clientId = config['CLIENT_ID']
    request.clientSecret = config['CLIENT_SECRET']
    deferred = client.send(request)
    deferred.addCallbacks(applicationAuthResponseCallback, onError)

# Setting optional client callbacks
client.setConnectedCallback(connected)
client.setDisconnectedCallback(disconnected)
client.setMessageReceivedCallback(onMessageReceived)

def calculate_lot(volume_in_cent, symbol_data):
    return float(volume_in_cent)/float(symbol_data.lotSize)*float(config['VOLUME_LEVERAGE'])

def cancelCtraderOrderRequest(message):
    request = ProtoOACancelOrderReq()
    request.orderId = message.order.orderId
    request.ctidTraderAccountId = account_id
    client.send(request)

def get_recent_bar_ctrader(symbolId, period, weeks):
    request = ProtoOAGetTrendbarsReq()
    request.ctidTraderAccountId = account_id
    request.period = period
    request.symbolId = symbolId
    request.fromTimestamp = int(calendar.timegm((datetime.utcnow() - timedelta(weeks=weeks)).utctimetuple())) * 1000
    request.toTimestamp = int(calendar.timegm(datetime.utcnow().utctimetuple())) * 1000
    return client.send(request)

def dumbFunc(message, mt5_info):
    print('Dumb called')


def preloadSymbolData(message, mt5_info, callback):
    global symbols
    symbol = find_symbol_by_id(symbols, message.order.tradeData.symbolId)
    deferred = cache.get_symbol_data_sync(symbol)
    deferred.addCallback(lambda symbol_data: callback(symbol_data, message, mt5_info))

def db_check_health():
    global db_fail_check
    e = connection_pool.check_health()
    if e == True:
        db_fail_check = 0
        print('\nCheck DB health OK')
    else:
        db_fail_check = db_fail_check + 1
        print('\nCheck DB health fail, retry in 120s')

if __name__ == '__main__':
    init_access_token_db()
    # Starting the client service
    client.startService()
    # Run Twisted reactor, we imported it earlier
    reactor.run()

