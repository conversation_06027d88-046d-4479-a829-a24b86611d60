import requests
import logging
import os


def _make_internal_request(config, endpoint, data=None, method='POST'):
    """
    Helper function to make internal API requests with proper authentication.

    Args:
        config (dict): Configuration dictionary containing API settings
        endpoint (str): API endpoint path (e.g., 'mt5_escape_window')
        data (dict): Request data payload
        method (str): HTTP method (default: 'POST')

    Returns:
        dict: Response data or None if request failed
    """
    api_base_url = config.get('API_BASE_URL', os.environ.get('API_BASE_URL', 'http://web:80'))
    auth_key = config.get('INTERNAL_API_AUTH_KEY', os.environ.get('INTERNAL_API_AUTH_KEY', ''))

    url = f"{api_base_url}/api/{endpoint}"
    headers = {'X-Internal-Auth-Key': auth_key}

    if data is None:
        data = {}

    # Add auth_key to data payload as well for compatibility
    data['auth_key'] = auth_key

    try:
        if method.upper() == 'POST':
            response = requests.post(url, headers=headers, data=data, timeout=10)
        elif method.upper() == 'GET':
            response = requests.get(url, headers=headers, params=data, timeout=10)
        else:
            logging.error(f"Unsupported HTTP method: {method}")
            return None

        if response.status_code == 200:
            return response.json()
        else:
            logging.error(f"InternalAPI request failed: {response.status_code} - {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        logging.error(f"Request exception: {str(e)}")
        return None


def send_mt5_escape_signal(config, container_name=None):
    """
    Send escape signal to MT5 container via API request.

    Args:
        config (dict): Configuration dictionary containing API settings
        container_name (str): Optional container name override

    Returns:
        bool: True if successful, False otherwise
    """
    data = {
        'container_name': container_name or config.get('MT5_URL', 'mt5')
    }

    result = _make_internal_request(config, 'mt5_escape_window', data)

    if result and result.get('success'):
        logging.info(f"Successfully sent escape signal to MT5 container: {data['container_name']}")
        return True
    else:
        logging.error(f"Failed to send escape signal to MT5 container: {data['container_name']}")
        return False


def search_mt5_server(config, server_name, container_name=None):
    """
    Search for MT5 server in container via API request.

    Args:
        config (dict): Configuration dictionary containing API settings
        server_name (str): Name of the server to search for
        container_name (str): Optional container name override

    Returns:
        dict: Server search result or None if failed
    """
    if not server_name:
        logging.error("Server name is required for MT5 server search")
        return None

    data = {
        'server_name': server_name,
        'container_name': container_name or config.get('MT5_URL', 'mt5')
    }

    result = _make_internal_request(config, 'mt5_server_search', data)

    if result and result.get('success'):
        logging.info(f"Successfully searched for MT5 server '{server_name}' in container: {data['container_name']}")
        return result
    else:
        logging.error(f"Failed to search for MT5 server '{server_name}' in container: {data['container_name']}")
        return None


def send_script_notification(config, script_id, status, message=None):
    """
    Send script notification via API request.

    Args:
        config (dict): Configuration dictionary containing API settings
        script_id (int): ID of the script
        status (str): Status of the script
        message (str): Optional notification message

    Returns:
        bool: True if successful, False otherwise
    """
    if not script_id or not status:
        logging.error("Script ID and status are required for script notification")
        return False

    data = {
        'script_id': script_id,
        'status': status
    }

    if message:
        data['message'] = message

    result = _make_internal_request(config, 'script_notification', data)

    if result and result.get('success'):
        logging.info(f"Successfully sent script notification for script {script_id} with status: {status}")
        return True
    else:
        logging.error(f"Failed to send script notification for script {script_id}")
        return False


def restart_bot(config, script_id):
    """
    Request bot restart via API request.

    Args:
        config (dict): Configuration dictionary containing API settings
        script_id (int): ID of the script/bot to restart

    Returns:
        bool: True if restart request was queued successfully, False otherwise
    """
    if not script_id:
        logging.error("Script ID is required for bot restart")
        return False

    data = {
        'script_id': script_id
    }

    result = _make_internal_request(config, 'bot_restart', data)

    if result and result.get('success'):
        logging.info(f"Successfully queued bot restart request for script {script_id}")
        return True
    else:
        logging.error(f"Failed to queue bot restart request for script {script_id}")
        return False


def get_expect_running_script_ids(config):
    """
    Get list of script IDs that are expected to be running.

    Args:
        config (dict): Configuration dictionary containing API settings

    Returns:
        list: List of script IDs that should be running, or empty list if failed
    """
    result = _make_internal_request(config, 'get_expect_running_script_ids', method='GET')

    if result and result.get('success'):
        script_ids = result.get('script_ids', [])
        logging.info(f"Retrieved {len(script_ids)} scripts expected to be running: {script_ids}")
        return script_ids
    else:
        logging.error("Failed to retrieve expected running script IDs")
        return []


def update_script_expect_status(config, script_id, expect_status):
    """
    Update the expect_status of a script in the database.

    Args:
        config (dict): Configuration dictionary containing API settings
        script_id (int): ID of the script
        expect_status (str): Expected status ('running', 'stopped', 'error')

    Returns:
        bool: True if successful, False otherwise
    """
    if not script_id or expect_status is None:
        logging.error("Script ID and expect_status are required")
        return False

    data = {
        'script_id': script_id,
        'expect_status': expect_status
    }

    result = _make_internal_request(config, 'update_script_expect_status', data)

    if result and result.get('success'):
        logging.info(f"Successfully updated expect_status for script {script_id} to: {expect_status}")
        return True
    else:
        logging.error(f"Failed to update expect_status for script {script_id}")
        return False
