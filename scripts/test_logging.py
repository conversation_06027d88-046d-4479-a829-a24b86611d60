#!/usr/bin/env python3
"""
Test script to verify that logging is working correctly in both main.py style (Twisted) 
and tasks.py style (standard logging) environments.
"""

import os
import sys

# Test 1: Import db_driver and test its logging
print("=== Testing db_driver logging ===")
try:
    from db_driver import ConnectionPool, log_debug, log_info, log_warning, log_error
    
    # Test the unified logging functions
    log_debug("This is a debug message from db_driver test")
    log_info("This is an info message from db_driver test")
    log_warning("This is a warning message from db_driver test")
    log_error("This is an error message from db_driver test")
    
    print("✓ db_driver logging functions work correctly")
except Exception as e:
    print(f"✗ Error testing db_driver logging: {e}")

# Test 2: Import tasks and test its logging
print("\n=== Testing tasks.py logging ===")
try:
    # Set environment variables for testing
    os.environ['CONNECTION_STRING'] = 'postgresql://test:test@localhost/test'
    os.environ['CELERY_BROKER_URL'] = 'amqp://localhost'
    os.environ['LOG_LEVEL'] = 'DEBUG'
    
    # Import tasks module (this will initialize the logger)
    import tasks
    
    # Test the logger
    tasks.logger.debug("This is a debug message from tasks test")
    tasks.logger.info("This is an info message from tasks test")
    tasks.logger.warning("This is a warning message from tasks test")
    tasks.logger.error("This is an error message from tasks test")
    
    print("✓ tasks.py logging works correctly")
except Exception as e:
    print(f"✗ Error testing tasks.py logging: {e}")

# Test 3: Test that logs go to stdout
print("\n=== Testing log output destination ===")
try:
    import logging
    import sys
    
    # Create a test logger similar to tasks.py
    test_logger = logging.getLogger('test_logger')
    test_logger.setLevel(logging.DEBUG)
    
    # Add stdout handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    test_logger.addHandler(handler)
    
    test_logger.debug("This debug message should go to stdout")
    test_logger.info("This info message should go to stdout")
    
    print("✓ Logging to stdout works correctly")
except Exception as e:
    print(f"✗ Error testing stdout logging: {e}")

print("\n=== Logging test completed ===")
print("If you see log messages above with timestamps, the logging is working correctly!")
print("Debug messages should appear in stdout.log, not stderr.log")
