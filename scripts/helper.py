
from dotenv import dotenv_values
from datetime import datetime, timedelta
import os
import pytz

config = {
    **dotenv_values("./scripts/dev.env"),
    **dotenv_values("dev.env"),
    **os.environ,  # override loaded values with environment variables
    **dotenv_values("custom.env"),
    **dotenv_values("./scripts/custom.env"),
}


def get_time_market_open(symbol_data):
    # Get the current date and time
    schedule = symbol_data.schedule
    timezone = pytz.timezone(symbol_data.scheduleTimeZone)
    system_timezone = pytz.timezone(config['TZ'])
    current_time = datetime.now(timezone)
    # Find the most recent Sunday
    days_since_sunday = current_time.weekday()  # 0 for Monday, 6 for Sunday
    # If today is Sunday, last_sunday should be today
    if current_time.weekday() == 6:
        last_sunday = current_time
        open_day = 0
    else:
        last_sunday = current_time - timedelta(days=days_since_sunday+1)

    last_sunday = last_sunday.replace(hour=0, minute=0, second=0, microsecond=0)

    if current_time.weekday() in (0, 1, 2, 3, 4):
        open_day = current_time.weekday()
    if current_time.weekday() == 5:
        open_day = 0
        last_sunday = last_sunday + timedelta(days=7)

    seconds_from_last_sunday = schedule[open_day].startSecond
    open_time = last_sunday + timedelta(seconds=seconds_from_last_sunday)

    while open_time < current_time:
        open_day = open_day + 1
        if open_day < 5:
            seconds_from_last_sunday = schedule[open_day].startSecond
            open_time = last_sunday + timedelta(seconds=seconds_from_last_sunday)
        else:
            last_sunday = last_sunday + timedelta(days=7)
            seconds_from_last_sunday = schedule[0].startSecond
            open_time = last_sunday + timedelta(seconds=seconds_from_last_sunday)
            break
    return open_time.astimezone(system_timezone)

def is_market_open(symbol_data):
    # Get the current date and time
    schedule = symbol_data.schedule
    timezone = pytz.timezone(symbol_data.scheduleTimeZone)
    current_time = datetime.now(timezone)
    # Find the most recent Sunday
    days_since_sunday = current_time.weekday()  # 0 for Monday, 6 for Sunday

    if days_since_sunday > len(schedule) - 1:
        return False
    # If today is Sunday, last_sunday should be today
    if current_time.weekday() == 6:
        last_sunday = current_time
    else:
        last_sunday = current_time - timedelta(days=days_since_sunday+1)

    last_sunday = last_sunday.replace(hour=0, minute=0, second=0, microsecond=0)

    open_time = last_sunday + timedelta(seconds=schedule[current_time.weekday()].startSecond)
    close_time = last_sunday + timedelta(seconds=schedule[current_time.weekday()].endSecond)

    return open_time <= current_time < close_time
