#!/bin/bash

# entry_path="/mainscripts/startscript.py"
# log_path="/mainscripts/logs"
# entry_wine_path=`winepath -w ${entry_path}`
# log_wine_path=`winepath -w ${log_path}`
# PYTHONUNBUFFERED=1 wine cmd.exe /c "@echo off && python ${entry_wine_path} > ${log_wine_path}\output.log 2 > ${log_wine_path}\error.log"

cd /scripts

# Create logs directory with proper permissions
mkdir -p /scripts/logs
chmod 777 /scripts/logs  # rwxrwxrwx: Everyone can read/write/execute

# Make sure any existing log files are also readable and writable by everyone
find /scripts/logs -type f -exec chmod 666 {} \;

# Set umask to ensure new log files are readable and writable by others
umask 000  # This ensures files are created with 666 permissions (rw-rw-rw-)

# Install dependencies
pip3 install --upgrade pip
pip3 install --no-deps -r /scripts/requirements.txt

# Start the application
python3 -u /scripts/app.py
