{"cells": [{"cell_type": "code", "execution_count": null, "id": "e1ed23ec", "metadata": {}, "outputs": [], "source": ["# %pip install ctrader-open-api requests pyOpenSSL service_identity psycopg2-binary pandas\n", "# %pip install mt5linux python-dotenv"]}, {"cell_type": "code", "execution_count": 7, "id": "0d9d1740", "metadata": {}, "outputs": [], "source": ["from dotenv import dotenv_values\n", "import os\n", "\n", "config = {\n", "    **dotenv_values(\"dev.env\"),  # load sensitive variables\n", "    **os.environ,  # override loaded values with environment variables\n", "}"]}, {"cell_type": "code", "execution_count": 22, "id": "4ab2a19d", "metadata": {}, "outputs": [], "source": ["from ctrader_open_api import Client, Protobuf, TcpProtocol, Auth, EndPoints\n", "from ctrader_open_api.messages.OpenApiCommonMessages_pb2 import *\n", "from ctrader_open_api.messages.OpenApiMessages_pb2 import *\n", "from ctrader_open_api.messages.OpenApiModelMessages_pb2 import *\n", "from twisted.internet import reactor, defer\n", "import json, pickle\n", "from db_driver import *\n", "import datetime\n", "import calendar"]}, {"cell_type": "code", "execution_count": 8, "id": "c05509b1", "metadata": {}, "outputs": [], "source": ["symbols = None\n", "account_id = int(config['ACCOUNT_ID'])\n", "connection_pool = ConnectionPool(connection_string=config['CONNECTION_STRING'])\n", "host = EndPoints.PROTOBUF_LIVE_HOST if config['HOST_TYPE'].lower() == \"live\" else EndPoints.PROTOBUF_DEMO_HOST\n", "client = Client(host, EndPoints.PROTOBUF_PORT, TcpProtocol)"]}, {"cell_type": "code", "execution_count": 9, "id": "26920cdf", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>order_ota</th>\n", "      <th>position_id</th>\n", "      <th>ota</th>\n", "      <th>account_id</th>\n", "      <th>open_order_id</th>\n", "      <th>status</th>\n", "      <th>pending_changes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [id, order_ota, position_id, ota, account_id, open_order_id, status, pending_changes]\n", "Index: []"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["connection_pool.get_slave_order_by_open_position_id(*********, account_id, 'cTrader')"]}, {"cell_type": "code", "execution_count": 23, "id": "cf5a010a", "metadata": {}, "outputs": [], "source": ["from mt5linux import MetaTrader5\n", "mt5 = MetaTrader5(host=config['MT5_URL'], port=8001)\n", "\n", "# Connect to MetaTrader 5 terminal\n", "if not mt5.initialize(login=int(config['MT5_LOGIN']), server=config['MT5_SERVER'], password=config['MT5_PASSWORD']):\n", "    print(\"initialize() mt5 failed\")\n", "    mt5.shutdown()\n", "    quit()"]}, {"cell_type": "code", "execution_count": 11, "id": "c21ab9b8", "metadata": {}, "outputs": [{"data": {"text/plain": ["7"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["mt5.TRADE_ACTION_MODIFY"]}, {"cell_type": "code", "execution_count": 13, "id": "d0a0e238", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n"]}], "source": ["print(mt5.orders_get(********))"]}, {"cell_type": "code", "execution_count": null, "id": "688262e9", "metadata": {}, "outputs": [], "source": ["********"]}, {"cell_type": "code", "execution_count": 9, "id": "7eead505", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["mt5.SYMBOL_TRADE_EXECUTION_MARKET"]}, {"cell_type": "code", "execution_count": null, "id": "334564c5", "metadata": {}, "outputs": [], "source": ["print(mt5.symbol_info('XAUUSD'))"]}, {"cell_type": "code", "execution_count": null, "id": "71cc0ace", "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "import pytz\n", "\n", "def get_time_remaining_market_open(seconds_from_last_sunday):\n", "    # Get the current date and time\n", "    timezone = pytz.timezone(config['TZ'])\n", "    current_time = datetime.now(timezone)\n", "    # Find the most recent Sunday\n", "    days_since_sunday = current_time.weekday()  # 0 for Monday, 6 for Sunday\n", "    # If today is Sunday, last_sunday should be today\n", "    if current_time.weekday() == 6:\n", "        last_sunday = current_time\n", "    else:\n", "        last_sunday = current_time - timed<PERSON>ta(days=days_since_sunday)\n", "    # Set the time to 0:00\n", "    last_sunday = last_sunday.replace(hour=0, minute=0, second=0, microsecond=0)\n", "    # Calculate the total number of seconds since Sunday\n", "    seconds_since_sunday = (current_time - last_sunday).total_seconds()\n", "\n", "    # Calculate the final time after adding 86,460 seconds\n", "    final_time = last_sunday + <PERSON><PERSON><PERSON>(seconds=seconds_from_last_sunday)\n", "    print(\"Final time:\", final_time)\n", "    return int(seconds_from_last_sunday - seconds_since_sunday) + 1\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "04a3f816", "metadata": {}, "outputs": [], "source": ["get_time_remaining_market_open(432060)"]}, {"cell_type": "code", "execution_count": null, "id": "8f9194d0", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import pytz\n", "\n", "# Specify the time zone you want to use\n", "timezone = pytz.timezone(config['TZ'])  # Example: Eastern Time (ET)\n", "\n", "# Get the current time in the specified time zone\n", "current_time = datetime.now(timezone)\n", "\n", "print(\"Current time in\", timezone.zone, \":\", current_time)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c5b1fb82", "metadata": {}, "outputs": [], "source": ["def find_symbol_by_id(symbols, target_id):\n", "    if symbols is None:\n", "        return None, -1\n", "\n", "    index_to_replace = -1\n", "    symbol_found = None\n", "    for i, symbol in enumerate(symbols):\n", "        if symbol.symbolId == target_id:\n", "            index_to_replace = i\n", "            symbol_found = symbol\n", "            break\n", "    return symbol_found, index_to_replace"]}, {"cell_type": "code", "execution_count": null, "id": "3b06bbe3", "metadata": {}, "outputs": [], "source": ["class SymbolCache:\n", "    def __init__(self):\n", "        self.cache = {}\n", "\n", "    def get_symbol_data_sync(self, symbol):\n", "        if symbol.symbolName in self.cache:\n", "            d = defer.Deferred()\n", "            d.callback(ProtoOASymbol.FromString(self.cache[symbol.symbolName]))\n", "            return d\n", "        else:\n", "            return self.fetch_symbol_data_async(symbol)\n", "\n", "    def fetch_symbol_callback(self, result_fetch, symbol):\n", "        result_fetch = Protobuf.extract(result_fetch)\n", "        symbol_data = result_fetch.symbol[0]\n", "        self.cache[symbol.symbolName] = symbol_data.SerializeToString()\n", "        save_detail_symbols_cache.delay(pickle.dumps(self.cache))\n", "        return symbol_data\n", "\n", "\n", "    def fetch_symbol_data_async(self, symbol):\n", "        request = ProtoOASymbolByIdReq()\n", "        request.ctidTraderAccountId = account_id\n", "        request.symbolId.extend([symbol.symbolId])\n", "        deferred = client.send(request)\n", "        return deferred.addCallback(lambda result: self.fetch_symbol_callback(result, symbol))\n", "\n", "cache = SymbolCache()"]}, {"cell_type": "code", "execution_count": null, "id": "07d91bdc", "metadata": {}, "outputs": [], "source": ["def symbolsResponseCallback(result):\n", "    print(\"\\nSymbols received\")\n", "    global symbols\n", "    symbols = Protobuf.extract(result).symbol\n", "\n", "def accountAuthResponseCallback(result):\n", "    print(\"\\nAccount authenticated\")\n", "    request = ProtoOASymbolsListReq()\n", "    request.ctidTraderAccountId = account_id\n", "    request.includeArchivedSymbols = False\n", "    deferred = client.send(request)\n", "    deferred.addCallbacks(symbolsResponseCallback, onError)\n", "\n", "def applicationAuthResponseCallback(result):\n", "    print(\"\\nApplication authenticated\")\n", "    request = ProtoOAAccountAuthReq()\n", "    request.ctidTraderAccountId = account_id\n", "    request.accessToken = config[\"ACCESS_TOKEN\"]\n", "    deferred = client.send(request)\n", "    deferred.addCallbacks(accountAuthResponseCallback, onError)\n", "\n", "def onError(client, failure): # Call back for errors\n", "    print(\"\\nMessage Error = \", failure)\n", "\n", "def disconnected(client, reason): # Callback for client disconnection\n", "    print(\"\\nDisconnected = \", reason)\n", "\n", "def onMessageReceived(client, message): # Callback for receiving all messages\n", "    if message.payloadType in [ProtoHeartbeatEvent().payloadType, ProtoOAAccountAuthRes().payloadType, ProtoOAApplicationAuthRes().payloadType, \\\n", "                            ProtoOASymbolsListRes().payloadType, ProtoOAGetTrendbarsRes().payloadType, ProtoOASymbolByIdRes().payloadType]:\n", "        return\n", "\n", "    print(\"\\nMessage type = \\n\", message.payloadType)\n", "    print(\"\\nMessage received = \\n\", Protobuf.extract(message))\n", "\n", "    if message.payloadType == ProtoOAExecutionEvent().payloadType:\n", "        executionEventHandler(Protobuf.extract(message))\n", "\n", "def connected(client): # Callback for client connection\n", "    print(\"\\nConnected\")\n", "    request = ProtoOAApplicationAuthReq()\n", "    request.clientId = config['CLIENT_ID']\n", "    request.clientSecret = config['CLIENT_SECRET']\n", "    deferred = client.send(request)\n", "    deferred.addCallbacks(applicationAuthResponseCallback, onError)\n", "\n", "# Setting optional client callbacks\n", "client.setConnectedCallback(connected)\n", "client.setDisconnectedCallback(disconnected)\n", "client.setMessageReceivedCallback(onMessageReceived)"]}, {"cell_type": "code", "execution_count": null, "id": "2568ee3a", "metadata": {}, "outputs": [], "source": ["def executionEventHandler(message):\n", "    if message.ctidTraderAccountId != account_id:\n", "        return\n", "    type_need_preload_symbol_data = [2, 4]\n", "    if message.executionType in type_need_preload_symbol_data:\n", "        preloadSymbolData(message, execution_handlers.get(message.executionType))\n", "    else:\n", "        execution_handlers.get(message.executionType)(message)"]}, {"cell_type": "code", "execution_count": null, "id": "51d25ea6", "metadata": {}, "outputs": [], "source": ["def calculate_lot(volume_in_cent, symbol_data):\n", "    return float(volume_in_cent)/float(symbol_data.lotSize)*float(config['VOLUME_LEVERAGE'])"]}, {"cell_type": "code", "execution_count": null, "id": "9628d5fd", "metadata": {}, "outputs": [], "source": ["\n", "def copyAcceptedOrder(symbol_data, message):\n", "    global symbols\n", "    symbol, index = find_symbol_by_id(symbols, message.order.tradeData.symbolId)\n", "    if symbol is None:\n", "        print('There is no symbol id' + str(message.order.tradeData.symbolId))\n", "        return\n", "\n", "    if  message.order.orderType == 2 and message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_CREATED: #Limit order\n", "        print('Make new copy limit order accept call')\n", "        # Save open order\n", "        order = {\n", "            'order_ota': message.order.orderId,\n", "            'position_id': message.position.positionId,\n", "            'ota': 'c<PERSON><PERSON><PERSON>',\n", "            'account_id': config['ACCOUNT_ID'],\n", "            'status': ConnectionPool.ORDER_STATUS['open']\n", "        }\n", "        inserted_df = connection_pool.insert_open_orders(order)\n", "        if inserted_df is None:\n", "            print('Inserted df is None, return')\n", "            return\n", "        lot = calculate_lot(message.order.tradeData.volume, symbol_data)\n", "        price = message.order.limitPrice  # Limit order price\n", "        sl = 0.0\n", "        tp = 0.0\n", "        if message.order.stopLoss != 0 or message.order.relativeStopLoss != 0:\n", "            sl = message.order.stopLoss or (price - message.order.relativeStopLoss*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price + message.order.relativeStopLoss*10**-5)\n", "        if message.order.takeProfit != 0 or message.order.relativeTakeProfit != 0:\n", "            tp = message.order.takeProfit or (price + message.order.relativeTakeProfit*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price - message.order.relativeTakeProfit*10**-5)\n", "        comment = \"ct id\" + str(message.order.orderId)  # Comment for the order\n", "        orderType = {\n", "            ProtoOATradeSide.BUY: mt5.ORDER_TYPE_BUY_LIMIT,\n", "            ProtoOATradeSide.SELL: mt5.ORDER_TYPE_SELL_LIMIT\n", "        }\n", "        # Loading price to void missing price error on MT5\n", "        mt5.symbol_info_tick(symbol.symbolName).ask\n", "        mt5.symbol_info_tick(symbol.symbolName).bid\n", "        # Prepare order request\n", "        request = {\n", "            \"action\": mt5.TRADE_ACTION_PENDING,\n", "            \"type\": orderType.get(message.order.tradeData.tradeSide),\n", "            \"symbol\": symbol.symbolName,\n", "            \"volume\": lot,\n", "            \"price\": price,\n", "            \"sl\": sl,\n", "            \"tp\": tp,\n", "            \"comment\": comment,\n", "            \"magic\": 2268,\n", "            \"type_filling\": mt5.ORDER_FILLING_IOC,\n", "            \"type_time\": mt5.ORDER_TIME_GTC\n", "        }\n", "\n", "        # Place the order\n", "        result = mt5.order_send(request)\n", "\n", "        if result.retcode != mt5.TRADE_RETCODE_DONE:\n", "            print(f\"Failed to place order: {result.comment}\")\n", "        else:\n", "            slave_order = {\n", "                'ota': 'mt5',\n", "                'account_id': config['MT5_LOGIN'],\n", "                'open_order_id': str(inserted_df['id'][0])\n", "            }\n", "            if result.retcode == mt5.TRADE_RETCODE_MARKET_CLOSED:\n", "                slave_order['status'] = ConnectionPool.ORDER_STATUS['waiting']\n", "            else:\n", "                print(\"Order placed successfully\")\n", "                slave_order['status'] = ConnectionPool.ORDER_STATUS['open']\n", "                slave_order['order_ota'] = result.order\n", "                slave_order['position_id'] = result.order\n", "            connection_pool.insert_slave_orders(slave_order)\n", "\n", "    if message.order.orderType == 1: # Market order\n", "        if message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_CREATED or \\\n", "            message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_OPEN:\n", "\n", "            if message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_CREATED:\n", "                order = {\n", "                    'order_ota': message.order.orderId,\n", "                    'position_id': message.position.positionId,\n", "                    'ota': 'c<PERSON><PERSON><PERSON>',\n", "                    'account_id': config['ACCOUNT_ID'],\n", "                    'status': ConnectionPool.ORDER_STATUS['open']\n", "                }\n", "                inserted_df = connection_pool.insert_open_orders(order)\n", "                if inserted_df is None:\n", "                    print('Inserted df is None, return')\n", "                    return\n", "            else:\n", "                is_complete_closed = True if message.order.tradeData.volume == message.position.tradeData.volume else False\n", "                if is_complete_closed:\n", "                    connection_pool.update_status_open_order(message.order.orderId, config['ACCOUNT_ID'], 'cTrader', ConnectionPool.ORDER_STATUS['closed'])\n", "                slave_orders_df = connection_pool.get_slave_order_by_open_position_id(message.position.positionId, config['ACCOUNT_ID'], 'cTrader')\n", "                if slave_orders_df.empty:\n", "                    print(\"Can not find any slave order\")\n", "                    return\n", "\n", "            is_buy_side = True if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else False\n", "            lot = calculate_lot(message.order.tradeData.volume, symbol_data)\n", "            price = mt5.symbol_info_tick(symbol.symbolName).ask if is_buy_side else mt5.symbol_info_tick(symbol.symbolName).bid # Market price\n", "            sl = 0.0\n", "            tp = 0.0\n", "            if message.order.stopLoss != 0 or message.order.relativeStopLoss != 0:\n", "                sl = message.order.stopLoss or (price - message.order.relativeStopLoss*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price + message.order.relativeStopLoss*10**-5)\n", "            if message.order.takeProfit != 0 or message.order.relativeTakeProfit != 0:\n", "                tp = message.order.takeProfit or (price + message.order.relativeTakeProfit*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price - message.order.relativeTakeProfit*10**-5)\n", "            comment = \"ct id\" + str(message.order.orderId)  # Comment for the order\n", "            orderType = {\n", "                ProtoOATradeSide.BUY: mt5.ORDER_TYPE_BUY_LIMIT,\n", "                ProtoOATradeSide.SELL: mt5.ORDER_TYPE_SELL_LIMIT\n", "            }\n", "            position_id = int(slave_orders_df['position_id'][0]) if message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_OPEN else None\n", "            result = mt5_send_market_order(symbol.symbolName, lot, is_buy_side, is_buy_side == False, sl, tp, position_id, comment)\n", "            if result.retcode != mt5.TRADE_RETCODE_DONE:\n", "                print(f\"Failed to place market order: {result.comment}\")\n", "            else:\n", "                print(f\"Comment MT5 return: {result.comment}\")\n", "                if message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_CREATED:\n", "                    slave_order = {\n", "                        'ota': 'mt5',\n", "                        'account_id': config['MT5_LOGIN'],\n", "                        'open_order_id': int(inserted_df['id'][0])\n", "                    }\n", "                    if result.retcode == mt5.TRADE_RETCODE_MARKET_CLOSED:\n", "                        print('Market closed')\n", "                        slave_order['status'] = ConnectionPool.ORDER_STATUS['waiting']\n", "                    else:\n", "                        print(\"Order placed successfully\")\n", "                        slave_order['status'] = ConnectionPool.ORDER_STATUS['open']\n", "                        slave_order['order_ota'] = result.order\n", "                        slave_order['position_id'] = result.order\n", "                    connection_pool.insert_slave_orders(slave_order)\n", "                else:\n", "                    if is_complete_closed:\n", "                        connection_pool.update_status_slave_order(slave_orders_df['order_ota'][0], config['MT5_LOGIN'], 'mt5', ConnectionPool.ORDER_STATUS['closed'])\n", "                        print('Update status slave order closed successfully')\n", "                    else:\n", "                        print('Partial close trade detected')\n", "\n", "    if message.order.orderType == ProtoOAOrderType.STOP_LOSS_TAKE_PROFIT and \\\n", "        message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_OPEN:\n", "\n", "        comment = \"cid \" + str(message.order.orderId)  # Comment for the order\n", "\n", "        orderType = {\n", "            ProtoOATradeSide.BUY: mt5.ORDER_TYPE_BUY_LIMIT,\n", "            ProtoOATradeSide.SELL: mt5.ORDER_TYPE_SELL_LIMIT\n", "        }\n", "        slave_orders = connection_pool.get_slave_order_by_open_position_id(message.order.positionId, config['ACCOUNT_ID'], 'cTrader')\n", "\n", "        if slave_orders.empty:\n", "            print(\"Not found any slave order match to cancel\")\n", "            return\n", "\n", "        # Prepare order request\n", "        request = {\n", "            \"action\": mt5.TRADE_ACTION_SLTP,\n", "            \"position\": int(slave_orders['position_id'][0]),\n", "            \"magic\": 2268,\n", "            \"sl\": message.position.stop<PERSON><PERSON>,\n", "            \"tp\": message.position.takeProfit\n", "        }\n", "        # Copy cancelled the order\n", "        result = mt5.order_send(request)\n", "\n", "        if result.retcode != mt5.TRADE_RETCODE_DONE:\n", "            print(f\"Failed to modify TP SL position: {result.comment}\")\n", "        else:\n", "            print(\"Modify TPSL position successfully\")\n"]}, {"cell_type": "code", "execution_count": 54, "id": "bdf86106", "metadata": {}, "outputs": [{"data": {"text/plain": ["3"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["mt5.symbol_info('XAUUSD').filling_mode"]}, {"cell_type": "code", "execution_count": 21, "id": "b71af112", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["None\n"]}], "source": ["r = mt5_send_market_order('EURUSD', 0.01, True, False, 0, 0, None)\n", "print(r)"]}, {"cell_type": "code", "execution_count": 44, "id": "cae8673b", "metadata": {}, "outputs": [], "source": ["deviation = 50\n", "symbol = 'XAUUSD'"]}, {"cell_type": "code", "execution_count": 45, "id": "b1ef6a3c", "metadata": {}, "outputs": [], "source": ["request={\n", "    \"action\": mt5.TRADE_ACTION_DEAL,\n", "    \"symbol\": symbol,\n", "    \"volume\": 0.01,\n", "    \"type\": mt5.ORDER_TYPE_BUY,\n", "    \"price\": mt5.symbol_info_tick(symbol).ask,\n", "    \"deviation\": deviation,\n", "    \"magic\": 234000,\n", "    \"comment\": \"python script close\",\n", "    \"type_time\": mt5.ORDER_TIME_GTC,\n", "    \"type_filling\": mt5.ORDER_FILLING_RETURN,\n", "}\n", "\n", "result=mt5.order_send(request)"]}, {"cell_type": "code", "execution_count": 53, "id": "da22c954", "metadata": {}, "outputs": [{"data": {"text/plain": ["2"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["mt5.ORDER_FILLING_RETURN"]}, {"cell_type": "code", "execution_count": 46, "id": "a62eaaf4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OrderSendResult(retcode=10009, deal=2514878784, order=2540095668, volume=0.1, price=2338.12, bid=2338.05, ask=2338.12, comment='Request executed', request_id=464393307, retcode_external=0, request=TradeRequest(action=1, magic=234000, order=0, symbol='XAUUSD', volume=0.1, price=2338.12, stoplimit=0.0, sl=0.0, tp=0.0, deviation=50, type=0, type_filling=2, type_time=0, expiration=0, comment='python script close', position=0, position_by=0))\n"]}], "source": ["print(result)"]}, {"cell_type": "code", "execution_count": 52, "id": "67aa1c0d", "metadata": {}, "outputs": [{"data": {"text/plain": ["1.08934"]}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["mt5.symbol_info_tick('EURUSD').ask"]}, {"cell_type": "code", "execution_count": 16, "id": "5eb964c7", "metadata": {}, "outputs": [], "source": ["def mt5_send_market_order(symbol, lot, buy, sell, sl, tp, id_position=None, comment=\" No specific comment\", magic=1111):\n", "\n", "    # Extract filling_mode\n", "    filling_type = mt5.ORDER_FILLING_RETURN\n", "\n", "\n", "    \"\"\" OPEN A TRADE \"\"\"\n", "    if buy and id_position==None:\n", "        request = {\n", "        \"action\": mt5.TRADE_ACTION_DEAL,\n", "        \"symbol\": symbol,\n", "        \"volume\": lot,\n", "        \"type\": mt5.ORDER_TYPE_BUY,\n", "        \"price\": mt5.symbol_info_tick(symbol).ask,\n", "        \"sl\": sl,\n", "        \"tp\": tp,\n", "        \"deviation\": 20,\n", "        \"magic\": magic,\n", "        \"comment\": comment,\n", "        \"type_filling\": filling_type,\n", "        \"type_time\": mt5.ORDER_TIME_GTC}\n", "\n", "        result = mt5.order_send(request)\n", "        return result\n", "\n", "    if sell and id_position==None:\n", "        request = {\n", "        \"action\": mt5.TRADE_ACTION_DEAL,\n", "        \"symbol\": symbol,\n", "        \"volume\": lot,\n", "        \"type\": mt5.ORDER_TYPE_SELL,\n", "        \"price\": mt5.symbol_info_tick(symbol).bid,\n", "        \"sl\": sl,\n", "        \"tp\": tp,\n", "        \"deviation\": 20,\n", "        \"magic\": magic,\n", "        \"comment\": comment,\n", "        \"type_filling\": filling_type,\n", "        \"type_time\": mt5.ORDER_TIME_GTC}\n", "\n", "        result = mt5.order_send(request)\n", "        return result\n", "\n", "\n", "    \"\"\" CLOSE A TRADE \"\"\"\n", "    if buy and id_position!=None:\n", "        request = {\n", "        \"position\": id_position,\n", "        \"action\": mt5.TRADE_ACTION_DEAL,\n", "        \"symbol\": symbol,\n", "        \"volume\": lot,\n", "        \"type\": mt5.ORDER_TYPE_BUY,\n", "        \"price\": mt5.symbol_info_tick(symbol).ask,\n", "        \"deviation\": 20,\n", "        \"magic\": magic,\n", "        \"comment\": comment,\n", "        \"type_filling\": filling_type,\n", "        \"type_time\": mt5.ORDER_TIME_GTC}\n", "\n", "        result = mt5.order_send(request)\n", "        return result\n", "\n", "    if sell and id_position!=None:\n", "        request = {\n", "        \"position\": id_position,\n", "        \"action\": mt5.TRADE_ACTION_DEAL,\n", "        \"symbol\": symbol,\n", "        \"volume\": lot,\n", "        \"type\": mt5.ORDER_TYPE_SELL,\n", "        \"price\": mt5.symbol_info_tick(symbol).bid,\n", "        \"deviation\": 20,\n", "        \"magic\": magic,\n", "        \"comment\": comment,\n", "        \"type_filling\": filling_type,\n", "        \"type_time\": mt5.ORDER_TIME_GTC}\n", "\n", "        result = mt5.order_send(request)\n", "        return result\n"]}, {"cell_type": "code", "execution_count": null, "id": "9a884471", "metadata": {}, "outputs": [], "source": ["\n", "def copyOrderCancelled(message):\n", "    if message.order.orderType == 2: #Limit order\n", "        print('Cancel limit order')\n", "        # Update status open order\n", "        connection_pool.update_status_open_order(message.order.orderId, account_id, 'cTrader', ConnectionPool.ORDER_STATUS['cancelled'])\n", "        # Get slave orders df\n", "        slave_orders = connection_pool.get_slave_order_by_open_position_id(message.order.positionId, account_id, 'cTrader')\n", "\n", "        if slave_orders.empty:\n", "            print(\"Not found any slave order match to cancel\")\n", "            return\n", "\n", "        # Prepare order request\n", "        request = {\n", "            \"action\": mt5.TRADE_ACTION_REMOVE,\n", "            \"order\": int(slave_orders['order_ota'][0]),\n", "            \"magic\": 2268\n", "        }\n", "        # Copy cancelled the order\n", "        result = mt5.order_send(request)\n", "        if result.retcode != mt5.TRADE_RETCODE_DONE:\n", "            print(f\"Failed to cancel order: {result.comment}\")\n", "        else:\n", "            print(\"Cancel order successfully\")\n", "            connection_pool.update_status_slave_order(slave_orders['order_ota'][0], config['MT5_LOGIN'], 'mt5', ConnectionPool.ORDER_STATUS['cancelled'])\n", "            print('Update status slave order closed successfully')\n", "    # Remove SL TP open position\n", "    if message.order.orderType == ProtoOAOrderType.STOP_LOSS_TAKE_PROFIT and \\\n", "        message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_OPEN:\n", "\n", "        print('Remove open position SL TP')\n", "\n", "        slave_orders = connection_pool.get_slave_order_by_open_position_id(message.order.positionId, account_id, 'cTrader')\n", "        if slave_orders.empty:\n", "            print(\"Not found any slave order match to cancel SL TP\")\n", "            return\n", "\n", "        # Prepare order request\n", "        request = {\n", "            \"action\": mt5.TRADE_ACTION_SLTP,\n", "            \"position\": int(slave_orders['position_id'][0]),\n", "            \"magic\": 2268,\n", "            \"sl\": 0.0,\n", "            \"tp\": 0.0\n", "        }\n", "        # Copy cancelled the order\n", "        result = mt5.order_send(request)\n", "        if result.retcode != mt5.TRADE_RETCODE_DONE:\n", "            print(f\"Failed to cancel TP SL position: {result.comment}\")\n", "        else:\n", "            print(\"Cancel TPSL position successfully\")"]}, {"cell_type": "code", "execution_count": null, "id": "953ecd8b", "metadata": {}, "outputs": [], "source": ["def copyModifiedOrder(symbol_data, message):\n", "    global symbols\n", "    if message.order.orderType == 2:\n", "        print(\"Order replaced call modified limit order\")\n", "\n", "        slave_orders = connection_pool.get_slave_order_by_open_position_id(message.order.positionId, account_id, 'cTrader')\n", "        symbol, index = find_symbol_by_id(symbols, message.order.tradeData.symbolId)\n", "        lot = calculate_lot(message.order.tradeData.volume, symbol_data)\n", "        price = message.order.limitPrice  # Limit order price\n", "        sl = 0.0\n", "        tp = 0.0\n", "        if message.order.stopLoss != 0 or message.order.relativeStopLoss != 0:\n", "            sl = message.order.stopLoss or (price - message.order.relativeStopLoss*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price + message.order.relativeStopLoss*10**-5)\n", "        if message.order.takeProfit != 0 or message.order.relativeTakeProfit != 0:\n", "            tp = message.order.takeProfit or (price + message.order.relativeTakeProfit*10**-5 if message.order.tradeData.tradeSide == ProtoOATradeSide.BUY else price - message.order.relativeTakeProfit*10**-5)\n", "        comment = \"ct id\" + str(message.order.orderId)  # Comment for the order\n", "        orderType = {\n", "            ProtoOATradeSide.BUY: mt5.ORDER_TYPE_BUY_LIMIT,\n", "            ProtoOATradeSide.SELL: mt5.ORDER_TYPE_SELL_LIMIT\n", "        }\n", "\n", "        # Prepare order request\n", "        request = {\n", "            \"action\": mt5.TRADE_ACTION_MODIFY,\n", "            \"type\": orderType.get(message.order.tradeData.tradeSide),\n", "            \"order\": int(slave_orders['order_ota'][0]),\n", "            \"symbol\": symbol.symbolName,\n", "            \"volume\": lot,\n", "            \"price\": price,\n", "            \"sl\": sl,\n", "            \"tp\": tp,\n", "            \"comment\": comment,\n", "            \"magic\": 2268,\n", "            \"type_filling\": mt5.ORDER_FILLING_IOC,\n", "            \"type_time\": mt5.ORDER_TIME_GTC\n", "        }\n", "        # Modified limit the order\n", "        result = mt5.order_send(request)\n", "\n", "        if result.retcode != mt5.TRADE_RETCODE_DONE:\n", "            print(f\"Failed to copy modified order: {result.comment}\")\n", "        else:\n", "            if result.retcode == mt5.TRADE_RETCODE_MARKET_CLOSED:\n", "                slave_order['status'] = ConnectionPool.ORDER_STATUS['waiting']\n", "            else:\n", "                print(\"Copy modified order successfully\")\n", "\n", "    if message.order.orderType == ProtoOAOrderType.STOP_LOSS_TAKE_PROFIT and \\\n", "        message.position.positionStatus == ProtoOAPositionStatus.POSITION_STATUS_OPEN:\n", "\n", "        print('Modify open position SL TP')\n", "\n", "        slave_orders = connection_pool.get_slave_order_by_open_position_id(message.order.positionId, account_id, 'cTrader')\n", "\n", "        if slave_orders.empty:\n", "            print(\"Not found any slave order match to modify SL TP\")\n", "            return\n", "\n", "        # Prepare order request\n", "        request = {\n", "            \"action\": mt5.TRADE_ACTION_SLTP,\n", "            \"position\": int(slave_orders['position_id'][0]),\n", "            \"magic\": 2268,\n", "            \"sl\": message.position.stop<PERSON><PERSON>,\n", "            \"tp\": message.position.takeProfit\n", "        }\n", "        # Copy cancelled the order\n", "        result = mt5.order_send(request)\n", "        if result.retcode != mt5.TRADE_RETCODE_DONE:\n", "            print(f\"Failed to modify TP SL position: {result.comment}\")\n", "        else:\n", "            print(\"Modify TPSL position successfully\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "40e3d802", "metadata": {}, "outputs": [], "source": ["def cancelCtraderOrderRequest(message):\n", "    request = ProtoOACancelOrderReq()\n", "    request.orderId = message.order.orderId\n", "    request.ctidTraderAccountId = account_id\n", "    client.send(request)"]}, {"cell_type": "code", "execution_count": null, "id": "bb4d6ab4", "metadata": {}, "outputs": [], "source": ["def dumbFunc(message):\n", "    print('Dumb called')"]}, {"cell_type": "code", "execution_count": null, "id": "199aab4c", "metadata": {}, "outputs": [], "source": ["def updateOrderFilled(message):\n", "    if message.order.orderType == ProtoOAOrderType.STOP_LOSS_TAKE_PROFIT:\n", "        print('Update status order get TP or SL')\n", "        connection_pool.update_status_open_order(message.order.orderId, account_id, 'cTrader', ConnectionPool.ORDER_STATUS['closed'])\n"]}, {"cell_type": "code", "execution_count": null, "id": "5640a043", "metadata": {}, "outputs": [], "source": ["def preloadSymbolData(message, callback):\n", "    global symbols\n", "    symbol, index = find_symbol_by_id(symbols, message.order.tradeData.symbolId)\n", "    deferred = cache.get_symbol_data_sync(symbol)\n", "    deferred.addCallback(lambda symbol_data: callback(symbol_data, message))\n"]}, {"cell_type": "code", "execution_count": null, "id": "ab3e5bcd", "metadata": {}, "outputs": [], "source": ["execution_handlers = {\n", "  2: copyAcceptedOrder, # ORDER_ACCEPTED\n", "  3: update<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, # ORDER_FILLED\n", "  4: copyModifiedOrder, # ORDER_REPLACED\n", "  5: copyOrderCancelled, # ORDER_CANCELLED\n", "  6: dumb<PERSON><PERSON><PERSON>, # ORDER_EXPIRED\n", "  7: dumb<PERSON><PERSON><PERSON>, # ORDER_REJECTED\n", "  8: <PERSON><PERSON><PERSON><PERSON>, # ORDER_CANCEL_REJECTED\n", "  9: <PERSON><PERSON><PERSON><PERSON>, # <PERSON><PERSON><PERSON>\n", "  10: <PERSON><PERSON><PERSON><PERSON>, # DEPOSIT_WITHDRAW\n", "  11: <PERSON><PERSON><PERSON><PERSON>, # ORDER_PARTIAL_FILL\n", "  12: <PERSON><PERSON><PERSON><PERSON>, # BONUS_DEPOSIT_WITHDRAW\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "98c9510e", "metadata": {}, "outputs": [], "source": ["# Starting the client service\n", "client.startService()\n", "# Run Twisted reactor, we imported it earlier\n", "reactor.run()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}